import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// AppDialog示例页面
class DialogExamplePage extends StatefulWidget {
  const DialogExamplePage({super.key});

  @override
  State<DialogExamplePage> createState() => _DialogExamplePageState();
}

class _DialogExamplePageState extends State<DialogExamplePage> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('基础对话框'),
          _buildDescription('最基本的对话框示例，包含标题、内容和默认的底部按钮。'),
          _buildExampleCard(
            child: AppButton(
              text: '显示基础对话框',
              type: ButtonType.primary,
              onPressed: () {
                AppDialog.show(
                  context: context,
                  title: '基础对话框',
                  child: const Padding(
                    padding: EdgeInsets.symmetric(vertical: 20),
                    child: Text('这是一个基础对话框示例，点击确定或取消按钮关闭。'),
                  ),
                );
              },
            ),
          ),

          _buildSectionTitle('自定义标题'),
          _buildDescription('对话框标题支持String或Widget类型，可以实现更复杂的标题布局。'),
          _buildExampleCard(
            child: Row(
              children: [
                AppButton(
                  text: '文本标题',
                  type: ButtonType.primary,
                  onPressed: () {
                    AppDialog.show(
                      context: context,
                      title: '自定义文本标题',
                      child: const Padding(
                        padding: EdgeInsets.symmetric(vertical: 20),
                        child: Text('使用String类型设置对话框标题。'),
                      ),
                    );
                  },
                ),
                const SizedBox(width: 16),
                AppButton(
                  text: 'Widget标题',
                  type: ButtonType.primary,
                  onPressed: () {
                    AppDialog.show(
                      context: context,
                      title: Row(
                        children: [
                          Icon(Icons.info, color: AppColors.primary),
                          const SizedBox(width: 8),
                          Text('带图标的标题', style: TextStyle(color: AppColors.primary)),
                        ],
                      ),
                      child: const Padding(
                        padding: EdgeInsets.symmetric(vertical: 20),
                        child: Text('使用Widget类型可以创建更复杂的标题布局。'),
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          _buildSectionTitle('自定义尺寸'),
          _buildDescription('可以通过width、height或constraints参数自定义对话框尺寸。'),
          _buildExampleCard(
            child: Row(
              children: [
                AppButton(
                  text: '小尺寸对话框',
                  type: ButtonType.primary,
                  onPressed: () {
                    AppDialog.show(
                      context: context,
                      title: '小尺寸对话框',
                      width: 300,
                      height: 200,
                      child: const Center(child: Text('这是一个固定尺寸的小对话框。')),
                    );
                  },
                ),
                const SizedBox(width: 16),
                AppButton(
                  text: '大尺寸对话框',
                  type: ButtonType.primary,
                  onPressed: () {
                    AppDialog.show(
                      context: context,
                      title: '大尺寸对话框',
                      width: 500,
                      constraints: const BoxConstraints(maxHeight: 400),
                      child: ListView.builder(
                        shrinkWrap: true,
                        physics: const ClampingScrollPhysics(),
                        itemCount: 20,
                        itemBuilder: (context, index) {
                          return ListTile(
                            title: Text('列表项 ${index + 1}'),
                            subtitle: Text('这是一个支持滚动的大对话框'),
                            leading: Icon(Icons.info),
                          );
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          _buildSectionTitle('超过高度滚动效果'),
          _buildDescription('超过高度滚动效果'),
          _buildExampleCard(
            child: AppButton(
              text: '显示超过高度对话框',
              type: ButtonType.primary,
              onPressed: () {
                AppDialog.show(
                  context: context,
                  title: '超过高度对话框',
                  width: 300,
                  height: 500,
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 20),
                    child: Column(
                      children: [...List.generate(100, (index) => Text(index.toString()))],
                    ),
                  ),
                );
              },
            ),
          ),

          _buildSectionTitle('异步操作处理'),
          _buildDescription('对话框支持异步操作，可用于表单提交、数据保存等场景。'),
          _buildExampleCard(
            child: AppButton(
              text: '带异步操作的对话框',
              type: ButtonType.primary,
              onPressed: () {
                AppDialog.show(
                  context: context,
                  title: '保存设置',
                  child: const Padding(
                    padding: EdgeInsets.symmetric(vertical: 20),
                    child: Text('点击确定按钮将模拟一个异步保存操作，按钮会显示加载状态。'),
                  ),
                  onConfirm: () async {
                    // 模拟一个异步操作
                    await Future.delayed(const Duration(seconds: 2));
                    // 操作完成后关闭对话框
                    if (context.mounted) {
                      context.pop();
                      // 显示操作成功提示
                      ToastManager.success('设置已保存');
                    }
                  },
                );
              },
            ),
          ),

          _buildSectionTitle('自定义底部区域'),
          _buildDescription('可以完全自定义对话框底部操作区域，或调整按钮的对齐方式。'),
          _buildExampleCard(
            child: Row(
              children: [
                AppButton(
                  text: '左对齐按钮',
                  type: ButtonType.primary,
                  onPressed: () {
                    AppDialog.show(
                      context: context,
                      title: '左对齐按钮',
                      footerAlignment: MainAxisAlignment.start,
                      child: const Padding(
                        padding: EdgeInsets.symmetric(vertical: 20),
                        child: Text('底部按钮左对齐显示。'),
                      ),
                    );
                  },
                ),
                const SizedBox(width: 16),
                AppButton(
                  text: '自定义底部',
                  type: ButtonType.primary,
                  onPressed: () {
                    AppDialog.show(
                      context: context,
                      title: '自定义底部区域',
                      child: const Padding(
                        padding: EdgeInsets.symmetric(vertical: 20),
                        child: Text('这个对话框有自定义的底部操作区域。'),
                      ),
                      footer: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          AppButton(
                            text: '取消',
                            type: ButtonType.default_,
                            onPressed: () => context.pop(),
                          ),
                          Row(
                            children: [
                              AppButton(
                                text: '保存草稿',
                                type: ButtonType.default_,
                                onPressed: () {
                                  context.pop();
                                  ToastManager.success('草稿已保存');
                                },
                              ),
                              const SizedBox(width: 12),
                              AppButton(
                                text: '发布',
                                type: ButtonType.primary,
                                onPressed: () {
                                  context.pop();
                                  ToastManager.success('已发布');
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          _buildSectionTitle('高级用法'),
          _buildDescription('结合其他组件创建更复杂的对话框内容。'),
          _buildExampleCard(
            child: AppButton(
              text: '表单对话框',
              type: ButtonType.primary,
              onPressed: () {
                _showFormDialog(context);
              },
            ),
          ),

          _buildSectionTitle('嵌套对话框'),
          _buildDescription('展示如何在一个对话框中打开另一个对话框，以及对话框之间的数据传递。'),
          _buildExampleCard(
            child: AppButton(
              text: '嵌套对话框',
              type: ButtonType.primary,
              onPressed: () {
                _showNestedDialog(context);
              },
            ),
          ),

          _buildSectionTitle('抽屉式弹窗'),
          _buildDescription('展示不同方向的抽屉式弹窗效果，支持从四个方向滑入。'),
          _buildExampleCard(
            child: Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                AppButton(
                  text: '右侧抽屉',
                  type: ButtonType.primary,
                  onPressed: () => _showDrawerDialog(context, SlideDirection.right),
                ),
                AppButton(
                  text: '左侧抽屉',
                  type: ButtonType.primary,
                  onPressed: () => _showDrawerDialog(context, SlideDirection.left),
                ),
                AppButton(
                  text: '顶部抽屉',
                  type: ButtonType.primary,
                  onPressed: () => _showDrawerDialog(context, SlideDirection.top),
                ),
                AppButton(
                  text: '底部抽屉',
                  type: ButtonType.primary,
                  onPressed: () => _showDrawerDialog(context, SlideDirection.bottom),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 显示嵌套对话框
  void _showNestedDialog(BuildContext context) {
    // 在StatefulBuilder外部声明状态变量
    String selectedOption = '未选择';

    // 使用StatefulBuilder管理对话框内部状态
    AppDialog.show(
      context: context,
      title: '主对话框',
      width: 400,
      child: StatefulBuilder(
        builder: (BuildContext context, StateSetter setState) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('这是主对话框，您可以从这里打开另一个对话框。'),
                const SizedBox(height: 20),
                Text('当前选择: $selectedOption', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 20),
                AppButton(
                  text: '打开次级对话框',
                  type: ButtonType.primary,
                  onPressed: () async {
                    // 打开次级对话框并等待结果
                    final result = await _showSecondaryDialog(context);

                    // 如果有返回结果，更新主对话框中的选择
                    if (result != null) {
                      setState(() {
                        selectedOption = result;
                      });
                    }
                  },
                ),
              ],
            ),
          );
        },
      ),
      onConfirm: () {
        // 确认按钮点击处理
        ToastManager.info('您选择了: $selectedOption');
        context.pop();
      },
    );
  }

  // 显示次级对话框
  Future<String?> _showSecondaryDialog(BuildContext context) {
    // 当前选中的选项状态
    String? currentSelection;

    return AppDialog.show<String>(
      context: context,
      title: '次级对话框',
      width: 350,
      child: StatefulBuilder(
        builder: (context, setState) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('请选择一个选项:'),
                const SizedBox(height: 16),
                _buildSelectionButton(context, '选项 A', currentSelection, (option) {
                  setState(() => currentSelection = option);
                }),
                const SizedBox(height: 8),
                _buildSelectionButton(context, '选项 B', currentSelection, (option) {
                  setState(() => currentSelection = option);
                }),
                const SizedBox(height: 8),
                _buildSelectionButton(context, '选项 C', currentSelection, (option) {
                  setState(() => currentSelection = option);
                }),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    AppButton(
                      text: '取消',
                      type: ButtonType.default_,
                      onPressed: () => context.pop(),
                    ),
                    const SizedBox(width: 12),
                    AppButton(
                      text: '确认',
                      type: ButtonType.primary,
                      // 只有选择了选项才启用确认按钮
                      disabled: currentSelection == null,
                      onPressed: () => context.pop(currentSelection),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
      // 使用自定义底部区域
      showFooter: false,
    );
  }

  // 构建选择按钮
  Widget _buildSelectionButton(
    BuildContext context,
    String option,
    String? currentSelection,
    Function(String) onSelect,
  ) {
    final bool isSelected = option == currentSelection;

    return SizedBox(
      width: double.infinity,
      child: AppButton(
        text: option,
        // 高亮显示当前选中的选项
        type: isSelected ? ButtonType.primary : ButtonType.default_,
        onPressed: () => onSelect(option),
      ),
    );
  }

  // 显示包含表单的对话框
  void _showFormDialog(BuildContext context) {
    final TextEditingController nameController = TextEditingController();
    final TextEditingController passwordController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    AppDialog.show(
      context: context,
      title: '用户信息表单',
      width: 400,
      child: Form(
        key: formKey,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppInput(
                label: '姓名',
                controller: nameController,
                hintText: '请输入您的姓名',
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入姓名';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              AppInput(
                label: '密码',
                controller: passwordController,
                hintText: '请输入密码',
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入密码';
                  }
                  if (value.length < 6) {
                    return '密码长度不能小于6位';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      onConfirm: () {
        // 表单验证通过，执行提交操作
        if (formKey.currentState!.validate()) {
          context.pop();
          ToastManager.success('提交成功: ${nameController.text}, 密码: ${passwordController.text}');
        }
      },
    );
  }

  // 显示抽屉式弹窗
  void _showDrawerDialog(BuildContext context, SlideDirection direction) {
    AppDialog.show(
      context: context,
      title: '抽屉弹窗',
      isDrawer: true,
      slideDirection: direction,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('这是一个抽屉式弹窗。'),
            const SizedBox(height: 20),
            Text('抽屉式弹窗的特点：'),
            const SizedBox(height: 10),
            Text('支持从四个方向滑入'),
            Text('可以自定义宽度和高度'),
            Text('支持异步操作和表单提交'),
            Text('可以完全自定义内容'),
          ],
        ),
      ),
    );
  }

  // 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 8),
      child: Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
    );
  }

  // 构建描述文本
  Widget _buildDescription(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(text, style: TextStyle(color: Colors.grey[700])),
    );
  }

  // 构建示例卡片
  Widget _buildExampleCard({required Widget child}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: .3)),
        borderRadius: BorderRadius.circular(4),
      ),
      child: child,
    );
  }
}
