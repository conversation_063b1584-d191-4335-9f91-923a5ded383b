import 'package:flutter/material.dart';

class DashedLine extends StatelessWidget {
  final double width;
  final double height;
  final double dashWidth;
  final double dashSpace;
  final Color color;
  final Axis direction;

  const DashedLine({
    this.width = 1,
    this.height = 100,
    this.dashWidth = 4,
    this.dashSpace = 2,
    this.color = Colors.black,
    this.direction = Axis.horizontal,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(
        painter: _DashedLinePainter(
          dashWidth: dashWidth,
          dashSpace: dashSpace,
          color: color,
          direction: direction,
        ),
      ),
    );
  }
}

class _DashedLinePainter extends CustomPainter {
  final double dashWidth;
  final double dashSpace;
  final Color color;
  final Axis direction;

  _DashedLinePainter({
    required this.dashWidth,
    required this.dashSpace,
    required this.color,
    required this.direction,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..strokeWidth =
              direction == Axis.horizontal ? size.height : size.width;

    double start = 0;
    final max = direction == Axis.horizontal ? size.width : size.height;
    while (start < max) {
      if (direction == Axis.horizontal) {
        canvas.drawLine(
          Offset(start, size.height / 2),
          Offset(start + dashWidth, size.height / 2),
          paint,
        );
      } else {
        canvas.drawLine(
          Offset(size.width / 2, start),
          Offset(size.width / 2, start + dashWidth),
          paint,
        );
      }
      start += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
