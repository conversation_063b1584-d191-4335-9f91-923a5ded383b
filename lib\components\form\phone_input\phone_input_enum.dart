import 'package:octasync_client/imports.dart';

/// 部门与成员枚举
class PhonmeInputEnum {
  /// 私有构造函数，防止实例化
  PhonmeInputEnum._();

  /// 区号选项列表
  static const List<SelectOption<String>> areaCodeOptions = [
    SelectOption(
      extra: {"english_name": "China", "chinese_name": "中国", "country_code": "CN"},
      value: "+86",
      label: "+86",
    ),
    SelectOption(
      extra: {"english_name": "Hong Kong", "chinese_name": "中国香港", "country_code": "HK"},
      value: "+852",
      label: "+852",
    ),
    SelectOption(
      extra: {"english_name": "Taiwan", "chinese_name": "中国台湾", "country_code": "TW"},
      value: "+886",
      label: "+886",
    ),
    SelectOption(
      extra: {"english_name": "Afghanistan", "chinese_name": "阿富汗", "country_code": "AF"},
      value: "+93",
      label: "+93",
    ),
    SelectOption(
      extra: {"english_name": "Alaska", "chinese_name": "阿拉斯加", "country_code": "US"},
      value: "+1907",
      label: "+1907",
    ),
    SelectOption(
      extra: {"english_name": "Albania", "chinese_name": "阿尔巴尼亚", "country_code": "AL"},
      value: "+355",
      label: "+355",
    ),
    SelectOption(
      extra: {"english_name": "Algeria", "chinese_name": "阿尔及利亚", "country_code": "DZ"},
      value: "+213",
      label: "+213",
    ),
    SelectOption(
      extra: {"english_name": "American Samoa", "chinese_name": "美属萨摩亚", "country_code": "AS"},
      value: "+1684",
      label: "+1684",
    ),
    SelectOption(
      extra: {"english_name": "Andorra", "chinese_name": "安道尔", "country_code": "AD"},
      value: "+376",
      label: "+376",
    ),
    SelectOption(
      extra: {"english_name": "Angola", "chinese_name": "安哥拉", "country_code": "AO"},
      value: "+244",
      label: "+244",
    ),
    SelectOption(
      extra: {"english_name": "Anguilla", "chinese_name": "安圭拉", "country_code": "AI"},
      value: "+1264",
      label: "+1264",
    ),
    SelectOption(
      extra: {
        "english_name": "Antigua and Barbuda",
        "chinese_name": "安提瓜和巴布达",
        "country_code": "AG",
      },
      value: "+1268",
      label: "+1268",
    ),
    SelectOption(
      extra: {"english_name": "Argentina", "chinese_name": "阿根廷", "country_code": "AR"},
      value: "+54",
      label: "+54",
    ),
    SelectOption(
      extra: {"english_name": "Armenia", "chinese_name": "亚美尼亚", "country_code": "AM"},
      value: "+374",
      label: "+374",
    ),
    SelectOption(
      extra: {"english_name": "Aruba", "chinese_name": "阿鲁巴", "country_code": "AW"},
      value: "+297",
      label: "+297",
    ),
    SelectOption(
      extra: {"english_name": "Ascension", "chinese_name": "阿森松", "country_code": "SH"},
      value: "+247",
      label: "+247",
    ),
    SelectOption(
      extra: {"english_name": "Australia", "chinese_name": "澳大利亚", "country_code": "AU"},
      value: "+61",
      label: "+61",
    ),
    SelectOption(
      extra: {"english_name": "Austria", "chinese_name": "奥地利", "country_code": "AT"},
      value: "+43",
      label: "+43",
    ),
    SelectOption(
      extra: {"english_name": "Azerbaijan", "chinese_name": "阿塞拜疆", "country_code": "AZ"},
      value: "+994",
      label: "+994",
    ),
    SelectOption(
      extra: {"english_name": "Bahamas", "chinese_name": "巴哈马", "country_code": "BS"},
      value: "+1242",
      label: "+1242",
    ),
    SelectOption(
      extra: {"english_name": "Bahrain", "chinese_name": "巴林", "country_code": "BH"},
      value: "+973",
      label: "+973",
    ),
    SelectOption(
      extra: {"english_name": "Bangladesh", "chinese_name": "孟加拉国", "country_code": "BD"},
      value: "+880",
      label: "+880",
    ),
    SelectOption(
      extra: {"english_name": "Barbados", "chinese_name": "巴巴多斯", "country_code": "BB"},
      value: "+1246",
      label: "+1246",
    ),
    SelectOption(
      extra: {"english_name": "Belarus", "chinese_name": "白俄罗斯", "country_code": "BY"},
      value: "+375",
      label: "+375",
    ),
    SelectOption(
      extra: {"english_name": "Belgium", "chinese_name": "比利时", "country_code": "BE"},
      value: "+32",
      label: "+32",
    ),
    SelectOption(
      extra: {"english_name": "Belize", "chinese_name": "伯利兹", "country_code": "BZ"},
      value: "+501",
      label: "+501",
    ),
    SelectOption(
      extra: {"english_name": "Benin", "chinese_name": "贝宁", "country_code": "BJ"},
      value: "+229",
      label: "+229",
    ),
    SelectOption(
      extra: {"english_name": "Bermuda", "chinese_name": "百慕大群岛", "country_code": "BM"},
      value: "+1441",
      label: "+1441",
    ),
    SelectOption(
      extra: {"english_name": "Bhutan", "chinese_name": "不丹", "country_code": "BT"},
      value: "+975",
      label: "+975",
    ),
    SelectOption(
      extra: {"english_name": "Bolivia", "chinese_name": "玻利维亚", "country_code": "BO"},
      value: "+591",
      label: "+591",
    ),
    SelectOption(
      extra: {
        "english_name": "Bosnia and Herzegovina",
        "chinese_name": "波斯尼亚和黑塞哥维那",
        "country_code": "BA",
      },
      value: "+387",
      label: "+387",
    ),
    SelectOption(
      extra: {"english_name": "Botswana", "chinese_name": "博茨瓦纳", "country_code": "BW"},
      value: "+267",
      label: "+267",
    ),
    SelectOption(
      extra: {"english_name": "Brazil", "chinese_name": "巴西", "country_code": "BR"},
      value: "+55",
      label: "+55",
    ),
    SelectOption(
      extra: {"english_name": "Brunei", "chinese_name": "文莱", "country_code": "BN"},
      value: "+673",
      label: "+673",
    ),
    SelectOption(
      extra: {"english_name": "Bulgaria", "chinese_name": "保加利亚", "country_code": "BG"},
      value: "+359",
      label: "+359",
    ),
    SelectOption(
      extra: {"english_name": "Burkina Faso", "chinese_name": "布基纳法索", "country_code": "BF"},
      value: "+226",
      label: "+226",
    ),
    SelectOption(
      extra: {"english_name": "Burundi", "chinese_name": "布隆迪", "country_code": "BI"},
      value: "+257",
      label: "+257",
    ),
    SelectOption(
      extra: {"english_name": "Cambodia", "chinese_name": "柬埔寨", "country_code": "KH"},
      value: "+855",
      label: "+855",
    ),
    SelectOption(
      extra: {"english_name": "Cameroon", "chinese_name": "喀麦隆", "country_code": "CM"},
      value: "+237",
      label: "+237",
    ),
    SelectOption(
      extra: {"english_name": "Canada", "chinese_name": "加拿大", "country_code": "CA"},
      value: "+1",
      label: "+1",
    ),
    SelectOption(
      extra: {"english_name": "Islas Canarias", "chinese_name": "加那利群岛", "country_code": "ES"},
      value: "+34",
      label: "+34",
    ),
    SelectOption(
      extra: {"english_name": "Cape Verde", "chinese_name": "开普", "country_code": "CV"},
      value: "+238",
      label: "+238",
    ),
    SelectOption(
      extra: {"english_name": "Cayman Islands", "chinese_name": "开曼群岛", "country_code": "KY"},
      value: "+1345",
      label: "+1345",
    ),
    SelectOption(
      extra: {
        "english_name": "Central African Republic",
        "chinese_name": "中非共和国",
        "country_code": "CF",
      },
      value: "+236",
      label: "+236",
    ),
    SelectOption(
      extra: {"english_name": "Chad", "chinese_name": "乍得", "country_code": "TD"},
      value: "+235",
      label: "+235",
    ),
    SelectOption(
      extra: {"english_name": "Chile", "chinese_name": "智利", "country_code": "CL"},
      value: "+56",
      label: "+56",
    ),
    SelectOption(
      extra: {"english_name": "Colombia", "chinese_name": "哥伦比亚", "country_code": "CO"},
      value: "+57",
      label: "+57",
    ),
    SelectOption(
      extra: {"english_name": "Comoros", "chinese_name": "科摩罗", "country_code": "KM"},
      value: "+269",
      label: "+269",
    ),
    SelectOption(
      extra: {
        "english_name": "Republic Of The Congo",
        "chinese_name": "刚果共和国",
        "country_code": "CG",
      },
      value: "+242",
      label: "+242",
    ),
    SelectOption(
      extra: {"english_name": "Cook Islands", "chinese_name": "库克群岛", "country_code": "CK"},
      value: "+682",
      label: "+682",
    ),
    SelectOption(
      extra: {"english_name": "Costa Rica", "chinese_name": "哥斯达黎加", "country_code": "CR"},
      value: "+506",
      label: "+506",
    ),
    SelectOption(
      extra: {"english_name": "Croatia", "chinese_name": "克罗地亚", "country_code": "HR"},
      value: "+385",
      label: "+385",
    ),
    SelectOption(
      extra: {"english_name": "Cuba", "chinese_name": "古巴", "country_code": "CU"},
      value: "+53",
      label: "+53",
    ),
    SelectOption(
      extra: {"english_name": "Curacao", "chinese_name": "库拉索", "country_code": "CW"},
      value: "+599",
      label: "+599",
    ),
    SelectOption(
      extra: {"english_name": "Cyprus", "chinese_name": "塞浦路斯", "country_code": "CY"},
      value: "+357",
      label: "+357",
    ),
    SelectOption(
      extra: {"english_name": "Czech", "chinese_name": "捷克", "country_code": "CZ"},
      value: "+420",
      label: "+420",
    ),
    SelectOption(
      extra: {"english_name": "Denmark", "chinese_name": "丹麦", "country_code": "DK"},
      value: "+45",
      label: "+45",
    ),
    SelectOption(
      extra: {"english_name": "Djibouti", "chinese_name": "吉布提", "country_code": "DJ"},
      value: "+253",
      label: "+253",
    ),
    SelectOption(
      extra: {"english_name": "Dominica", "chinese_name": "多米尼克", "country_code": "DM"},
      value: "+1767",
      label: "+1767",
    ),
    SelectOption(
      extra: {
        "english_name": "Dominican Republic",
        "chinese_name": "多米尼加共和国",
        "country_code": "DO",
      },
      value: "+1809",
      label: "+1809",
    ),
    SelectOption(
      extra: {"english_name": "Ecuador", "chinese_name": "厄瓜多尔", "country_code": "EC"},
      value: "+593",
      label: "+593",
    ),
    SelectOption(
      extra: {"english_name": "Egypt", "chinese_name": "埃及", "country_code": "EG"},
      value: "+20",
      label: "+20",
    ),
    SelectOption(
      extra: {"english_name": "El Salvador", "chinese_name": "萨尔瓦多", "country_code": "SV"},
      value: "+503",
      label: "+503",
    ),
    SelectOption(
      extra: {"english_name": "Equatorial Guinea", "chinese_name": "赤道几内亚", "country_code": "GQ"},
      value: "+240",
      label: "+240",
    ),
    SelectOption(
      extra: {"english_name": "Eritrea", "chinese_name": "厄立特里亚", "country_code": "ER"},
      value: "+291",
      label: "+291",
    ),
    SelectOption(
      extra: {"english_name": "Estonia", "chinese_name": "爱沙尼亚", "country_code": "EE"},
      value: "+372",
      label: "+372",
    ),
    SelectOption(
      extra: {"english_name": "Ethiopia", "chinese_name": "埃塞俄比亚", "country_code": "ET"},
      value: "+251",
      label: "+251",
    ),
    SelectOption(
      extra: {"english_name": "Falkland Islands", "chinese_name": "福克兰群岛", "country_code": "FK"},
      value: "+500",
      label: "+500",
    ),
    SelectOption(
      extra: {"english_name": "Faroe Islands", "chinese_name": "法罗群岛", "country_code": "FO"},
      value: "+298",
      label: "+298",
    ),
    SelectOption(
      extra: {"english_name": "Fiji", "chinese_name": "斐济", "country_code": "FJ"},
      value: "+679",
      label: "+679",
    ),
    SelectOption(
      extra: {"english_name": "Finland", "chinese_name": "芬兰", "country_code": "FI"},
      value: "+358",
      label: "+358",
    ),
    SelectOption(
      extra: {"english_name": "France", "chinese_name": "法国", "country_code": "FR"},
      value: "+33",
      label: "+33",
    ),
    SelectOption(
      extra: {"english_name": "French Guiana", "chinese_name": "法属圭亚那", "country_code": "GF"},
      value: "+594",
      label: "+594",
    ),
    SelectOption(
      extra: {"english_name": "French Polynesia", "chinese_name": "法属波利尼西亚", "country_code": "PF"},
      value: "+689",
      label: "+689",
    ),
    SelectOption(
      extra: {"english_name": "Gabon", "chinese_name": "加蓬", "country_code": "GA"},
      value: "+241",
      label: "+241",
    ),
    SelectOption(
      extra: {"english_name": "Gambia", "chinese_name": "冈比亚", "country_code": "GM"},
      value: "+220",
      label: "+220",
    ),
    SelectOption(
      extra: {"english_name": "Georgia", "chinese_name": "格鲁吉亚", "country_code": "GE"},
      value: "+995",
      label: "+995",
    ),
    SelectOption(
      extra: {"english_name": "Germany", "chinese_name": "德国", "country_code": "DE"},
      value: "+49",
      label: "+49",
    ),
    SelectOption(
      extra: {"english_name": "Ghana", "chinese_name": "加纳", "country_code": "GH"},
      value: "+233",
      label: "+233",
    ),
    SelectOption(
      extra: {"english_name": "Gibraltar", "chinese_name": "直布罗陀", "country_code": "GI"},
      value: "+350",
      label: "+350",
    ),
    SelectOption(
      extra: {"english_name": "Greece", "chinese_name": "希腊", "country_code": "GR"},
      value: "+30",
      label: "+30",
    ),
    SelectOption(
      extra: {"english_name": "Greenland", "chinese_name": "格陵兰岛", "country_code": "GL"},
      value: "+299",
      label: "+299",
    ),
    SelectOption(
      extra: {"english_name": "Grenada", "chinese_name": "格林纳达", "country_code": "GD"},
      value: "+1473",
      label: "+1473",
    ),
    SelectOption(
      extra: {"english_name": "Guadeloupe", "chinese_name": "瓜德罗普岛", "country_code": "GP"},
      value: "+590",
      label: "+590",
    ),
    SelectOption(
      extra: {"english_name": "Guam", "chinese_name": "关岛", "country_code": "GU"},
      value: "+1671",
      label: "+1671",
    ),
    SelectOption(
      extra: {"english_name": "Guatemala", "chinese_name": "瓜地马拉", "country_code": "GT"},
      value: "+502",
      label: "+502",
    ),
    SelectOption(
      extra: {"english_name": "Guinea", "chinese_name": "几内亚", "country_code": "GN"},
      value: "+224",
      label: "+224",
    ),
    SelectOption(
      extra: {"english_name": "Guinea-Bissau", "chinese_name": "几内亚比绍共和国", "country_code": "GW"},
      value: "+245",
      label: "+245",
    ),
    SelectOption(
      extra: {"english_name": "Guyana", "chinese_name": "圭亚那", "country_code": "GY"},
      value: "+592",
      label: "+592",
    ),
    SelectOption(
      extra: {"english_name": "Haiti", "chinese_name": "海地", "country_code": "HT"},
      value: "+509",
      label: "+509",
    ),
    SelectOption(
      extra: {"english_name": "Hawaii", "chinese_name": "夏威夷", "country_code": "US"},
      value: "+1808",
      label: "+1808",
    ),
    SelectOption(
      extra: {"english_name": "Honduras", "chinese_name": "洪都拉斯", "country_code": "HN"},
      value: "+504",
      label: "+504",
    ),

    SelectOption(
      extra: {"english_name": "Hungary", "chinese_name": "匈牙利", "country_code": "HU"},
      value: "+36",
      label: "+36",
    ),
    SelectOption(
      extra: {"english_name": "Iceland", "chinese_name": "冰岛", "country_code": "IS"},
      value: "+354",
      label: "+354",
    ),
    SelectOption(
      extra: {"english_name": "India", "chinese_name": "印度", "country_code": "IN"},
      value: "+91",
      label: "+91",
    ),
    SelectOption(
      extra: {"english_name": "Indonesia", "chinese_name": "印度尼西亚", "country_code": "ID"},
      value: "+62",
      label: "+62",
    ),
    SelectOption(
      extra: {"english_name": "Iran", "chinese_name": "伊朗", "country_code": "IR"},
      value: "+98",
      label: "+98",
    ),
    SelectOption(
      extra: {"english_name": "Iraq", "chinese_name": "伊拉克", "country_code": "IQ"},
      value: "+964",
      label: "+964",
    ),
    SelectOption(
      extra: {"english_name": "Ireland", "chinese_name": "爱尔兰", "country_code": "IE"},
      value: "+353",
      label: "+353",
    ),
    SelectOption(
      extra: {"english_name": "Israel", "chinese_name": "以色列", "country_code": "IL"},
      value: "+972",
      label: "+972",
    ),
    SelectOption(
      extra: {"english_name": "Italy", "chinese_name": "意大利", "country_code": "IT"},
      value: "+39",
      label: "+39",
    ),
    SelectOption(
      extra: {"english_name": "Ivory Coast", "chinese_name": "象牙海岸", "country_code": "CI"},
      value: "+225",
      label: "+225",
    ),
    SelectOption(
      extra: {"english_name": "Jamaica", "chinese_name": "牙买加", "country_code": "JM"},
      value: "+1876",
      label: "+1876",
    ),
    SelectOption(
      extra: {"english_name": "Japan", "chinese_name": "日本", "country_code": "JP"},
      value: "+81",
      label: "+81",
    ),
    SelectOption(
      extra: {"english_name": "Jordan", "chinese_name": "约旦", "country_code": "JO"},
      value: "+962",
      label: "+962",
    ),
    SelectOption(
      extra: {"english_name": "Kazakhstan", "chinese_name": "哈萨克斯坦", "country_code": "KZ"},
      value: "+7",
      label: "+7",
    ),
    SelectOption(
      extra: {"english_name": "Kenya", "chinese_name": "肯尼亚", "country_code": "KE"},
      value: "+254",
      label: "+254",
    ),
    SelectOption(
      extra: {"english_name": "Kiribati", "chinese_name": "基里巴斯", "country_code": "KI"},
      value: "+686",
      label: "+686",
    ),
    SelectOption(
      extra: {"english_name": "Korea Democratic Rep.", "chinese_name": "朝鲜", "country_code": "KP"},
      value: "+85",
      label: "+85",
    ),
    SelectOption(
      extra: {"english_name": "South Korea", "chinese_name": "韩国", "country_code": "KR"},
      value: "+82",
      label: "+82",
    ),
    SelectOption(
      extra: {"english_name": "Kuwait", "chinese_name": "科威特", "country_code": "KW"},
      value: "+965",
      label: "+965",
    ),
    SelectOption(
      extra: {"english_name": "Kyrgyzstan", "chinese_name": "吉尔吉斯斯坦", "country_code": "KG"},
      value: "+996",
      label: "+996",
    ),
    SelectOption(
      extra: {"english_name": "Laos", "chinese_name": "老挝", "country_code": "LA"},
      value: "+856",
      label: "+856",
    ),
    SelectOption(
      extra: {"english_name": "Latvia", "chinese_name": "拉脱维亚", "country_code": "LV"},
      value: "+371",
      label: "+371",
    ),
    SelectOption(
      extra: {"english_name": "Lebanon", "chinese_name": "黎巴嫩", "country_code": "LB"},
      value: "+961",
      label: "+961",
    ),
    SelectOption(
      extra: {"english_name": "Lesotho", "chinese_name": "莱索托", "country_code": "LS"},
      value: "+266",
      label: "+266",
    ),
    SelectOption(
      extra: {"english_name": "Liberia", "chinese_name": "利比里亚", "country_code": "LR"},
      value: "+231",
      label: "+231",
    ),
    SelectOption(
      extra: {"english_name": "Libya", "chinese_name": "利比亚", "country_code": "LY"},
      value: "+218",
      label: "+218",
    ),
    SelectOption(
      extra: {"english_name": "Liechtenstein", "chinese_name": "列支敦士登", "country_code": "LI"},
      value: "+423",
      label: "+423",
    ),
    SelectOption(
      extra: {"english_name": "Lithuania", "chinese_name": "立陶宛", "country_code": "LT"},
      value: "+370",
      label: "+370",
    ),
    SelectOption(
      extra: {"english_name": "Luxembourg", "chinese_name": "卢森堡", "country_code": "LU"},
      value: "+352",
      label: "+352",
    ),
    SelectOption(
      extra: {"english_name": "Macau", "chinese_name": "中国澳门", "country_code": "MO"},
      value: "+853",
      label: "+853",
    ),
    SelectOption(
      extra: {"english_name": "Macedonia", "chinese_name": "马其顿", "country_code": "MK"},
      value: "+389",
      label: "+389",
    ),
    SelectOption(
      extra: {"english_name": "Madagascar", "chinese_name": "马达加斯加", "country_code": "MG"},
      value: "+261",
      label: "+261",
    ),
    SelectOption(
      extra: {"english_name": "Malawi", "chinese_name": "马拉维", "country_code": "MW"},
      value: "+265",
      label: "+265",
    ),
    SelectOption(
      extra: {"english_name": "Malaysia", "chinese_name": "马来西亚", "country_code": "MY"},
      value: "+60",
      label: "+60",
    ),
    SelectOption(
      extra: {"english_name": "Maldives", "chinese_name": "马尔代夫", "country_code": "MV"},
      value: "+960",
      label: "+960",
    ),
    SelectOption(
      extra: {"english_name": "Mali", "chinese_name": "马里", "country_code": "ML"},
      value: "+223",
      label: "+223",
    ),
    SelectOption(
      extra: {"english_name": "Malta", "chinese_name": "马耳他", "country_code": "MT"},
      value: "+356",
      label: "+356",
    ),
    SelectOption(
      extra: {"english_name": "Marshall Islands", "chinese_name": "马绍尔群岛", "country_code": "MH"},
      value: "+692",
      label: "+692",
    ),
    SelectOption(
      extra: {"english_name": "Martinique", "chinese_name": "马提尼克", "country_code": "MQ"},
      value: "+596",
      label: "+596",
    ),
    SelectOption(
      extra: {"english_name": "Mauritania", "chinese_name": "毛里塔尼亚", "country_code": "MR"},
      value: "+222",
      label: "+222",
    ),
    SelectOption(
      extra: {"english_name": "Mauritius", "chinese_name": "毛里求斯", "country_code": "MU"},
      value: "+230",
      label: "+230",
    ),
    SelectOption(
      extra: {"english_name": "Mayotte", "chinese_name": "马约特", "country_code": "YT"},
      value: "+269",
      label: "+269",
    ),
    SelectOption(
      extra: {"english_name": "Mexico", "chinese_name": "墨西哥", "country_code": "MX"},
      value: "+52",
      label: "+52",
    ),
    SelectOption(
      extra: {"english_name": "Micronesia", "chinese_name": "密克罗尼西亚", "country_code": "FM"},
      value: "+691",
      label: "+691",
    ),
    SelectOption(
      extra: {"english_name": "Moldova", "chinese_name": "摩尔多瓦", "country_code": "MD"},
      value: "+373",
      label: "+373",
    ),
    SelectOption(
      extra: {"english_name": "Monaco", "chinese_name": "摩纳哥", "country_code": "MC"},
      value: "+377",
      label: "+377",
    ),
    SelectOption(
      extra: {"english_name": "Mongolia", "chinese_name": "蒙古", "country_code": "MN"},
      value: "+976",
      label: "+976",
    ),
    SelectOption(
      extra: {"english_name": "Montenegro", "chinese_name": "黑山", "country_code": "ME"},
      value: "+382",
      label: "+382",
    ),
    SelectOption(
      extra: {"english_name": "Montserrat", "chinese_name": "蒙特塞拉特岛", "country_code": "MS"},
      value: "+1664",
      label: "+1664",
    ),
    SelectOption(
      extra: {"english_name": "Morocco", "chinese_name": "摩洛哥", "country_code": "MA"},
      value: "+212",
      label: "+212",
    ),
    SelectOption(
      extra: {"english_name": "Mozambique", "chinese_name": "莫桑比克", "country_code": "MZ"},
      value: "+258",
      label: "+258",
    ),
    SelectOption(
      extra: {"english_name": "Myanmar", "chinese_name": "缅甸", "country_code": "MM"},
      value: "+95",
      label: "+95",
    ),
    SelectOption(
      extra: {"english_name": "Namibia", "chinese_name": "纳米比亚", "country_code": "NA"},
      value: "+264",
      label: "+264",
    ),
    SelectOption(
      extra: {"english_name": "Nauru", "chinese_name": "拿鲁岛", "country_code": "NR"},
      value: "+674",
      label: "+674",
    ),
    SelectOption(
      extra: {"english_name": "Nepal", "chinese_name": "尼泊尔", "country_code": "NP"},
      value: "+977",
      label: "+977",
    ),
    SelectOption(
      extra: {"english_name": "Netherlands", "chinese_name": "荷兰", "country_code": "NL"},
      value: "+31",
      label: "+31",
    ),
    SelectOption(
      extra: {"english_name": "New Caledonia", "chinese_name": "新喀里多尼亚", "country_code": "NC"},
      value: "+687",
      label: "+687",
    ),
    SelectOption(
      extra: {"english_name": "New Zealand", "chinese_name": "新西兰", "country_code": "NZ"},
      value: "+64",
      label: "+64",
    ),
    SelectOption(
      extra: {"english_name": "Nicaragua", "chinese_name": "尼加拉瓜", "country_code": "NI"},
      value: "+505",
      label: "+505",
    ),
    SelectOption(
      extra: {"english_name": "Niger", "chinese_name": "尼日尔", "country_code": "NE"},
      value: "+227",
      label: "+227",
    ),
    SelectOption(
      extra: {"english_name": "Nigeria", "chinese_name": "尼日利亚", "country_code": "NG"},
      value: "+234",
      label: "+234",
    ),
    SelectOption(
      extra: {"english_name": "Niue Island", "chinese_name": "纽埃岛(新)", "country_code": "NU"},
      value: "+683",
      label: "+683",
    ),
    SelectOption(
      extra: {"english_name": "Norfolk Island", "chinese_name": "诺福克岛(澳)", "country_code": "NF"},
      value: "+6723",
      label: "+6723",
    ),
    SelectOption(
      extra: {"english_name": "Norway", "chinese_name": "挪威", "country_code": "NO"},
      value: "+47",
      label: "+47",
    ),
    SelectOption(
      extra: {"english_name": "Oman", "chinese_name": "阿曼", "country_code": "OM"},
      value: "+968",
      label: "+968",
    ),
    SelectOption(
      extra: {"english_name": "Palau", "chinese_name": "帕劳", "country_code": "PW"},
      value: "+680",
      label: "+680",
    ),
    SelectOption(
      extra: {"english_name": "Panama", "chinese_name": "巴拿马", "country_code": "PA"},
      value: "+507",
      label: "+507",
    ),
    SelectOption(
      extra: {"english_name": "Papua New Guinea", "chinese_name": "巴布亚新几内亚", "country_code": "PG"},
      value: "+675",
      label: "+675",
    ),
    SelectOption(
      extra: {"english_name": "Paraguay", "chinese_name": "巴拉圭", "country_code": "PY"},
      value: "+595",
      label: "+595",
    ),
    SelectOption(
      extra: {"english_name": "Peru", "chinese_name": "秘鲁", "country_code": "PE"},
      value: "+51",
      label: "+51",
    ),
    SelectOption(
      extra: {"english_name": "Philippines", "chinese_name": "菲律宾", "country_code": "PH"},
      value: "+63",
      label: "+63",
    ),
    SelectOption(
      extra: {"english_name": "Poland", "chinese_name": "波兰", "country_code": "PL"},
      value: "+48",
      label: "+48",
    ),
    SelectOption(
      extra: {"english_name": "Portugal", "chinese_name": "葡萄牙", "country_code": "PT"},
      value: "+351",
      label: "+351",
    ),
    SelectOption(
      extra: {"english_name": "Pakistan", "chinese_name": "巴基斯坦", "country_code": "PK"},
      value: "+92",
      label: "+92",
    ),
    SelectOption(
      extra: {"english_name": "Puerto Rico", "chinese_name": "波多黎各", "country_code": "PR"},
      value: "+1787",
      label: "+1787",
    ),
    SelectOption(
      extra: {"english_name": "Qatar", "chinese_name": "卡塔尔", "country_code": "QA"},
      value: "+974",
      label: "+974",
    ),
    SelectOption(
      extra: {"english_name": "Réunion Island", "chinese_name": "留尼汪", "country_code": "RE"},
      value: "+262",
      label: "+262",
    ),
    SelectOption(
      extra: {"english_name": "Romania", "chinese_name": "罗马尼亚", "country_code": "RO"},
      value: "+40",
      label: "+40",
    ),
    SelectOption(
      extra: {"english_name": "Russia", "chinese_name": "俄罗斯", "country_code": "RU"},
      value: "+7",
      label: "+7",
    ),
    SelectOption(
      extra: {"english_name": "Rwanda", "chinese_name": "卢旺达", "country_code": "RW"},
      value: "+250",
      label: "+250",
    ),
    SelectOption(
      extra: {"english_name": "Samoa,Eastern", "chinese_name": "东萨摩亚(美)", "country_code": "AS"},
      value: "+684",
      label: "+684",
    ),
    SelectOption(
      extra: {"english_name": "Samoa", "chinese_name": "萨摩亚", "country_code": "WS"},
      value: "+685",
      label: "+685",
    ),
    SelectOption(
      extra: {"english_name": "San Marino", "chinese_name": "圣马力诺", "country_code": "SM"},
      value: "+378",
      label: "+378",
    ),
    SelectOption(
      extra: {
        "english_name": "Saint Pierre and Miquelon",
        "chinese_name": "圣彼埃尔和密克隆岛",
        "country_code": "PM",
      },
      value: "+508",
      label: "+508",
    ),
    SelectOption(
      extra: {
        "english_name": "Sao Tome and Principe",
        "chinese_name": "圣多美和普林西比",
        "country_code": "ST",
      },
      value: "+239",
      label: "+239",
    ),
    SelectOption(
      extra: {"english_name": "Saudi Arabia", "chinese_name": "沙特阿拉伯", "country_code": "SA"},
      value: "+966",
      label: "+966",
    ),
    SelectOption(
      extra: {"english_name": "Senegal", "chinese_name": "塞内加尔", "country_code": "SN"},
      value: "+221",
      label: "+221",
    ),
    SelectOption(
      extra: {"english_name": "Serbia", "chinese_name": "塞尔维亚", "country_code": "RS"},
      value: "+381",
      label: "+381",
    ),
    SelectOption(
      extra: {"english_name": "Seychelles", "chinese_name": "塞舌尔", "country_code": "SC"},
      value: "+248",
      label: "+248",
    ),
    SelectOption(
      extra: {"english_name": "Sierra Leone", "chinese_name": "塞拉利昂", "country_code": "SL"},
      value: "+232",
      label: "+232",
    ),
    SelectOption(
      extra: {"english_name": "Singapore", "chinese_name": "新加坡", "country_code": "SG"},
      value: "+65",
      label: "+65",
    ),
    SelectOption(
      extra: {
        "english_name": "Saint Maarten (Dutch Part)",
        "chinese_name": "圣马丁岛（荷兰部分）",
        "country_code": "SX",
      },
      value: "+1721",
      label: "+1721",
    ),
    SelectOption(
      extra: {"english_name": "Slovakia", "chinese_name": "斯洛伐克", "country_code": "SK"},
      value: "+421",
      label: "+421",
    ),
    SelectOption(
      extra: {"english_name": "Slovenia", "chinese_name": "斯洛文尼亚", "country_code": "SI"},
      value: "+386",
      label: "+386",
    ),
    SelectOption(
      extra: {"english_name": "Solomon Islands", "chinese_name": "所罗门群岛", "country_code": "SB"},
      value: "+677",
      label: "+677",
    ),
    SelectOption(
      extra: {"english_name": "Somalia", "chinese_name": "索马里", "country_code": "SO"},
      value: "+252",
      label: "+252",
    ),
    SelectOption(
      extra: {"english_name": "South Africa", "chinese_name": "南非", "country_code": "ZA"},
      value: "+27",
      label: "+27",
    ),
    SelectOption(
      extra: {"english_name": "Spain", "chinese_name": "西班牙", "country_code": "ES"},
      value: "+34",
      label: "+34",
    ),
    SelectOption(
      extra: {"english_name": "Sri Lanka", "chinese_name": "斯里兰卡", "country_code": "LK"},
      value: "+94",
      label: "+94",
    ),
    SelectOption(
      extra: {"english_name": "St.Helena", "chinese_name": "圣赫勒拿", "country_code": "SH"},
      value: "+290",
      label: "+290",
    ),
    SelectOption(
      extra: {"english_name": "Saint Lucia", "chinese_name": "圣露西亚", "country_code": "LC"},
      value: "+1758",
      label: "+1758",
    ),
    SelectOption(
      extra: {
        "english_name": "Saint Vincent and The Grenadines",
        "chinese_name": "圣文森特和格林纳丁斯",
        "country_code": "VC",
      },
      value: "+1784",
      label: "+1784",
    ),
    SelectOption(
      extra: {"english_name": "Sudan", "chinese_name": "苏丹", "country_code": "SD"},
      value: "+249",
      label: "+249",
    ),
    SelectOption(
      extra: {"english_name": "Suriname", "chinese_name": "苏里南", "country_code": "SR"},
      value: "+597",
      label: "+597",
    ),
    SelectOption(
      extra: {"english_name": "Swaziland", "chinese_name": "斯威士兰", "country_code": "SZ"},
      value: "+268",
      label: "+268",
    ),
    SelectOption(
      extra: {"english_name": "Sweden", "chinese_name": "瑞典", "country_code": "SE"},
      value: "+46",
      label: "+46",
    ),
    SelectOption(
      extra: {"english_name": "Switzerland", "chinese_name": "瑞士", "country_code": "CH"},
      value: "+41",
      label: "+41",
    ),
    SelectOption(
      extra: {"english_name": "Syria", "chinese_name": "叙利亚", "country_code": "SY"},
      value: "+963",
      label: "+963",
    ),
    SelectOption(
      extra: {"english_name": "Tajikistan", "chinese_name": "塔吉克斯坦", "country_code": "TJ"},
      value: "+992",
      label: "+992",
    ),
    SelectOption(
      extra: {"english_name": "Tanzania", "chinese_name": "坦桑尼亚", "country_code": "TZ"},
      value: "+255",
      label: "+255",
    ),
    SelectOption(
      extra: {"english_name": "Thailand", "chinese_name": "泰国", "country_code": "TH"},
      value: "+66",
      label: "+66",
    ),
    SelectOption(
      extra: {"english_name": "Timor-Leste", "chinese_name": "东帝汶", "country_code": "TL"},
      value: "+670",
      label: "+670",
    ),
    SelectOption(
      extra: {
        "english_name": "United Arab Emirates",
        "chinese_name": "阿拉伯联合酋长国",
        "country_code": "AE",
      },
      value: "+971",
      label: "+971",
    ),
    SelectOption(
      extra: {"english_name": "Togo", "chinese_name": "多哥", "country_code": "TG"},
      value: "+228",
      label: "+228",
    ),
    SelectOption(
      extra: {"english_name": "Tokelau Is.", "chinese_name": "托克劳群岛(新)", "country_code": "TK"},
      value: "+690",
      label: "+690",
    ),
    SelectOption(
      extra: {"english_name": "Tonga", "chinese_name": "汤加", "country_code": "TO"},
      value: "+676",
      label: "+676",
    ),
    SelectOption(
      extra: {
        "english_name": "Trinidad and Tobago",
        "chinese_name": "特立尼达和多巴哥",
        "country_code": "TT",
      },
      value: "+1868",
      label: "+1868",
    ),
    SelectOption(
      extra: {"english_name": "Tunisia", "chinese_name": "突尼斯", "country_code": "TN"},
      value: "+216",
      label: "+216",
    ),
    SelectOption(
      extra: {"english_name": "Turkey", "chinese_name": "土耳其", "country_code": "TR"},
      value: "+90",
      label: "+90",
    ),
    SelectOption(
      extra: {"english_name": "Turkmenistan", "chinese_name": "土库曼斯坦", "country_code": "TM"},
      value: "+993",
      label: "+993",
    ),
    SelectOption(
      extra: {
        "english_name": "Turks and Caicos Islands",
        "chinese_name": "特克斯和凯科斯群岛",
        "country_code": "TC",
      },
      value: "+1649",
      label: "+1649",
    ),
    SelectOption(
      extra: {"english_name": "Tuvalu", "chinese_name": "图瓦卢", "country_code": "TUV"},
      value: "+688",
      label: "+688",
    ),
    SelectOption(
      extra: {"english_name": "United States", "chinese_name": "美国", "country_code": "US"},
      value: "+1",
      label: "+1",
    ),
    SelectOption(
      extra: {"english_name": "Uganda", "chinese_name": "乌干达", "country_code": "UG"},
      value: "+256",
      label: "+256",
    ),
    SelectOption(
      extra: {"english_name": "Ukraine", "chinese_name": "乌克兰", "country_code": "UA"},
      value: "+380",
      label: "+380",
    ),
    SelectOption(
      extra: {"english_name": "United Kingdom", "chinese_name": "英国", "country_code": "GB"},
      value: "+44",
      label: "+44",
    ),
    SelectOption(
      extra: {"english_name": "Uruguay", "chinese_name": "乌拉圭", "country_code": "UY"},
      value: "+598",
      label: "+598",
    ),
    SelectOption(
      extra: {"english_name": "Uzbekistan", "chinese_name": "乌兹别克斯坦", "country_code": "UZ"},
      value: "+998",
      label: "+998",
    ),
    SelectOption(
      extra: {"english_name": "Vanuatu", "chinese_name": "瓦努阿图", "country_code": "VU"},
      value: "+678",
      label: "+678",
    ),
    SelectOption(
      extra: {"english_name": "Venezuela", "chinese_name": "委内瑞拉", "country_code": "VE"},
      value: "+58",
      label: "+58",
    ),
    SelectOption(
      extra: {"english_name": "Vietnam", "chinese_name": "越南", "country_code": "VN"},
      value: "+84",
      label: "+84",
    ),
    SelectOption(
      extra: {
        "english_name": "Virgin Islands, British",
        "chinese_name": "英属处女群岛",
        "country_code": "VG",
      },
      value: "+1340",
      label: "+1340",
    ),
    SelectOption(
      extra: {
        "english_name": "Virgin Islands, US",
        "chinese_name": "美属维尔京群岛",
        "country_code": "VI",
      },
      value: "+1284",
      label: "+1284",
    ),
    SelectOption(
      extra: {"english_name": "Wake I.", "chinese_name": "威克岛(美)", "country_code": "UM"},
      value: "+1808",
      label: "+1808",
    ),
    SelectOption(
      extra: {"english_name": "Yemen", "chinese_name": "也门", "country_code": "YE"},
      value: "+967",
      label: "+967",
    ),
    SelectOption(
      extra: {"english_name": "Zambia", "chinese_name": "赞比亚", "country_code": "ZM"},
      value: "+260",
      label: "+260",
    ),
    SelectOption(
      extra: {"english_name": "Zanzibar", "chinese_name": "桑给巴尔", "country_code": "TZ"},
      value: "+259",
      label: "+259",
    ),
    SelectOption(
      extra: {"english_name": "Zimbabwe", "chinese_name": "津巴布韦", "country_code": "ZW"},
      value: "+263",
      label: "+263",
    ),
  ];
}
