import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/src/app_basic_text_field.dart';

class AppHyperlink extends StatefulWidget {
  final String? initialValue;

  final ValueChanged<String>? onChanged;

  const AppHyperlink({
    super.key,
    this.initialValue,
    this.onChanged,
    // this.decoration,
  });

  @override
  State<AppHyperlink> createState() => _AppDateState();
}

var decoration = InputDecoration(
  border: OutlineInputBorder(borderSide: BorderSide(color: Colors.grey, width: 1)),
  contentPadding: EdgeInsets.symmetric(horizontal: 10),
  hintText: '',
);

class _AppDateState extends State<AppHyperlink> {
  String? _lastValue;

  @override
  void initState() {
    super.initState();
    // _initFormat();
    _lastValue = widget.initialValue;
  }

  @override
  void didUpdateWidget(covariant AppHyperlink oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  /// 验证超链接格式
  /// 如果是有效的超链接（http://或https://开头），返回字符串本身
  /// 否则返回空字符串
  String _validateHyperlink(String? input) {
    if (input == null || input.trim().isEmpty) {
      return '';
    }

    String trimmedInput = input.trim();
    if (trimmedInput.startsWith('http://') || trimmedInput.startsWith('https://')) {
      return trimmedInput;
    }

    return '';
  }

  @override
  Widget build(BuildContext context) {
    return AppBasicTextField(
      initialValue: widget.initialValue ?? '',
      keyboardType: TextInputType.datetime,

      formatOnFocus: (value) {
        return value.toString();
      },
      formatOnBlur: (value) {
        return value.toString();
      },
      parser: (text) {
        try {
          String? parsedValue;

          if (text.trim().isEmpty) {
            parsedValue = '';
          } else {
            try {
              parsedValue = _validateHyperlink(text);
            } catch (e) {
              print('错误信息：${e}');
            }
          }

          // 保存有效日期
          _lastValue = parsedValue;
          return parsedValue;
        } catch (e) {
          // 解析失败，返回上次有效日期或当前日期
          // print('日期格式转换失败: $e, 使用上次有效日期或当前日期');
          return _lastValue; // ?? DateTime.now();
        }
      },
      onParsedChanged: (value) {
        // 由于 AppBasicTextField 的 onParsedChanged 传递的是 String，
        // 我们需要重新解析它

        if (widget.onChanged != null) {
          try {
            String? parsedValue;
            if (value.isEmpty) {
              parsedValue = '';
              // parsedValue = _lastValue ?? DateTime.now();
            } else {
              parsedValue = _validateHyperlink(value);
            }

            // 更新最后有效日期
            _lastValue = parsedValue;
            widget.onChanged!(parsedValue);
          } catch (e) {
            // 如果解析失败，使用上次的有效日期
            if (value.trim().isEmpty) {
              widget.onChanged!('');
            } else {
              widget.onChanged!(_lastValue!);
            }
          }
        }
      },
      decoration: decoration,
    );
  }
}
