// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'wbs_field_config.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

WbsFieldConfig _$WbsFieldConfigFromJson(Map<String, dynamic> json) =>
    WbsFieldConfig(
      id: json['Id'] as String?,
      wbsConfigTypeenum: (json['WBSConfigTypeenum'] as num?)?.toInt(),
      orderIndex: (json['OrderIndex'] as num?)?.toInt() ?? 0,
      displayName: json['DisplayName'] as String? ?? '',
      dataTypeenum: json['DataTypeenum'],
      placeholder: json['Placeholder'],
      optionValues: json['OptionValues'],
      format: json['Format'],
      enabled: json['Enabled'] as bool? ?? false,
      visible: json['Visible'] as bool? ?? false,
      requiredCreate: json['RequiredCreate'] as bool? ?? false,
      requiredSubmit: json['RequiredSubmit'] as bool? ?? false,
      editable: json['Editable'] as bool? ?? false,
      disabledField: json['DisabledField'],
      fieldSourceenum: (json['FieldSourceenum'] as num?)?.toInt(),
    );

Map<String, dynamic> _$WbsFieldConfigToJson(WbsFieldConfig instance) =>
    <String, dynamic>{
      'Id': instance.id,
      'WBSConfigTypeenum': instance.wbsConfigTypeenum,
      'OrderIndex': instance.orderIndex,
      'DisplayName': instance.displayName,
      'DataTypeenum': instance.dataTypeenum,
      'Placeholder': instance.placeholder,
      'OptionValues': instance.optionValues,
      'Format': instance.format,
      'Enabled': instance.enabled,
      'Visible': instance.visible,
      'RequiredCreate': instance.requiredCreate,
      'RequiredSubmit': instance.requiredSubmit,
      'Editable': instance.editable,
      'DisabledField': instance.disabledField,
      'FieldSourceenum': instance.fieldSourceenum,
    };
