import 'package:flutter/material.dart';
import 'package:octasync_client/layout/window_bar/window_bar.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class WindowBarWrapper extends StatelessWidget {
  const WindowBarWrapper({required this.child, super.key});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(children: [if (AppUtil.isDesktop) const WindowBar(), Expanded(child: child)]),
    );
  }
}
