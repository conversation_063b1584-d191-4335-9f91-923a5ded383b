import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/message_controller.dart';
import '../models/message.dart';

class MessageListView extends GetView<MessageController> {
  const MessageListView({super.key});

  @override
  Widget build(BuildContext context) {
    final messageController = Get.put(MessageController());

    return Obx(() => ListView.builder(
      itemCount: messageController.messages.length,
      itemBuilder: (context, index) {
        final message = messageController.messages[index];
        return MessageListTile(message: message);
      },
    ));
  }
}

class MessageListTile extends StatelessWidget {
  final Message message;

  const MessageListTile({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        child: Text(message.senderName[0]),
      ),
      title: Text(message.senderName),
      subtitle: Text(message.content),
      trailing: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            '${message.timestamp.hour}:${message.timestamp.minute}',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
            ),
          ),
          if (!message.isRead)
            Container(
              margin: const EdgeInsets.only(top: 4),
              width: 8,
              height: 8,
              decoration: const BoxDecoration(
                color: Colors.red,
                shape: BoxShape.circle,
              ),
            ),
        ],
      ),
      onTap: () {
        Get.find<MessageController>().markAsRead(message.id);
      },
    );
  }
}
