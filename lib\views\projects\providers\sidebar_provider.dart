import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class SideBarProvider with ChangeNotifier {
  ///
  /// 私有变量区域
  ///
  bool _showSideBar = true; // 是否显示侧边栏
  int _checkedId = 1; // 默认选中
  final List<SideBarItem> _items = [
    SideBarItem(id: 1, icon: IconFont.xianxing_tixing, title: '项目', color: AppColors.purple),
    SideBarItem(id: 2, icon: IconFont.xianxing_tixing, title: '项目集', color: AppColors.yellow),
    SideBarItem(id: 3, icon: IconFont.xianxing_tixing, title: '指派单', color: AppColors.accent),
    SideBarItem(id: 4, icon: IconFont.xianxing_tixing, title: '配置中心', color: AppColors.cyan),
  ];

  ///
  /// 公开属性区域
  ///
  get showSideBar => _showSideBar;
  get items => _items;
  get currentCheckedId => _checkedId;
  get currentCheckedItem => _items.firstWhere((element) => element.id == _checkedId);

  ///
  /// 方法区域
  ///
  changeTotal({required int id, int total = 0}) {
    // var obj = _items.firstWhere((element) => element.id == id);
    // obj.total = total;
    // notifyListeners();

    final index = _items.indexWhere((element) => element.id == id);
    if (index != -1) {
      // 创建新的 item 实例以确保触发更新
      _items[index] = SideBarItem(
        id: _items[index].id,
        icon: _items[index].icon,
        title: _items[index].title,
        color: _items[index].color,
        total: total,
      );
      notifyListeners(); // 确保调用这个方法
    }
  }

  changeCheckedId(int id) {
    _checkedId = id;
    notifyListeners();
  }

  toggleSideBar() {
    _showSideBar = !_showSideBar;
    notifyListeners();
  }
}
