import 'package:flutter/cupertino.dart';
import 'package:octasync_client/utils/http_service.dart';

final _http = HttpService();

const controller = '/Business/Role/';

/// 企业角色管理
class RoleAuthManagementApi {
  static Future<dynamic> getList(data) {
    return _http.post('${controller}GetGroupList', data: data);
  }

  static Future<dynamic> getUsersByRoleId(data) {
    return _http.post('${controller}GetRoleEmployeeList', data: data);
  }

  static Future<dynamic> add(data) {
    return _http.post('${controller}AddOrEditGroup', data: data);
  }

  static Future<dynamic> del(data) {
    return _http.post('${controller}DeleteGroup', data: data);
  }

  static Future<dynamic> addOrEditRole(data) {
    return _http.post('${controller}AddOrEditRole', data: data);
  }

  static Future<dynamic> delRole(params) {
    return _http.post('${controller}DeleteRole', params: params);
  }

  static Future<dynamic> getRoleDetail(params) {
    return _http.post('${controller}GetRoleDetail', params: params);
  }
}
