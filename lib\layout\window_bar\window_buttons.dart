import 'package:flutter/material.dart';
import 'package:window_manager/window_manager.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class WindowButtons extends StatelessWidget {
  const WindowButtons({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: AppUtil.isMacOS ? _buildMacOSButtons() : _buildWindowsButtons(),
    );
  }

  List<Widget> _buildMacOSButtons() {
    return [
      const SizedBox(width: 6),
      _WindowButton(
        color: const Color(0xFFFF5F57),
        onPressed: () => windowManager.close(),
        isMacOS: true,
      ),
      const SizedBox(width: 8),
      _WindowButton(
        color: const Color(0xFFFFBD2E),
        onPressed: () => windowManager.minimize(),
        isMacOS: true,
      ),
      const SizedBox(width: 8),
      _WindowButton(
        color: const Color(0xFF28C840),
        onPressed: () async {
          if (await windowManager.isMaximized()) {
            await windowManager.unmaximize();
          } else {
            await windowManager.maximize();
          }
        },
        isMacOS: true,
      ),
    ];
  }

  List<Widget> _buildWindowsButtons() {
    return [
      _WindowButton(
        iconData: IconFont.xianxing_zuixiaohua,
        onPressed: () => windowManager.minimize(),
      ),
      _WindowButton(
        iconData: IconFont.xianxing_zuidahua,
        onPressed: () async {
          if (await windowManager.isMaximized()) {
            await windowManager.unmaximize();
          } else {
            await windowManager.maximize();
          }
        },
      ),
      _WindowButton(iconData: IconFont.xianxing_guanbi, onPressed: () => windowManager.close()),
      const SizedBox(width: 4),
    ];
  }
}

class _WindowButton extends StatelessWidget {
  final IconData? iconData;
  final VoidCallback onPressed;
  final Color? color;
  final bool isMacOS;

  const _WindowButton({required this.onPressed, this.iconData, this.color, this.isMacOS = false});

  @override
  Widget build(BuildContext context) {
    if (isMacOS) {
      return SizedBox(
        width: 12,
        height: 12,
        child: Material(
          color: color ?? Colors.transparent,
          shape: const CircleBorder(),
          child: InkWell(onTap: onPressed, customBorder: const CircleBorder()),
        ),
      );
    }

    return SizedBox(
      width: 46,
      height: 32,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(4),
          child: Center(child: Icon(iconData, size: AppIconSize.small, color: context.icon300)),
        ),
      ),
    );
  }
}
