import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class TrunkItem extends StatefulWidget {
  const TrunkItem({super.key});

  @override
  State<TrunkItem> createState() => _TrunkItemState();
}

class _TrunkItemState extends State<TrunkItem> {
  bool isShowTooltip1 = false;

  @override
  Widget build(BuildContext context) {
    var borderColor = Variables.commonBorderColor(context);

    final bool isCollection = false;

    List<DropdownItem> items = [
      DropdownItem(text: '选项1', value: 1, disabled: true, iconData: Icons.settings),
      DropdownItem(text: '选项2', value: 2, iconData: Icons.settings, divided: true),
      DropdownItem(text: '选项选项选项选项3', value: 3, iconData: IconFont.mianxing_huibao),
    ];

    List<DropdownItem> items2 = [
      DropdownItem(text: '选项1', value: 1, disabled: true, iconData: Icons.settings),
      DropdownItem(text: '选项2', value: 2, iconData: Icons.settings, divided: true),
      DropdownItem(text: '选项选项选项选项3', value: 3, iconData: IconFont.mianxing_huibao),
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: borderColor, width: 1.0),
        borderRadius: BorderRadius.all(Radius.circular(AppRadiusSize.radius4)),
      ),
      height: 500,
      width: double.infinity,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppTag(text: '规划'),
                SizedBox(width: 5),
                Expanded(
                  child: AppTooltip(
                    message: '中石油-商业项目-#123燃烧器-研发设计新型节能燃烧器',
                    child: Text(
                      '中石油-商业项目-#123燃烧器-研发设计新型节能燃烧器',
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),
                SizedBox(width: 5),

                AppButton(
                  iconData: Icons.star_rounded,
                  size: ButtonSize.small,
                  color: AppColors.yellow,
                  type: ButtonType.transparent,
                  onPressed: () {},
                ),

                AppDropdown(
                  size: DropdownSize.small,
                  type: DropdownType.transparent,
                  color: context.icon200,
                  iconData: Icons.more_vert,
                  // alwaysShow: true,
                  placement: DropdownPlacement.bottomRight,

                  // offset: const Offset(0, 8),
                  trigger: DropdownTrigger.click, // 可选hover或click
                  items: [
                    DropdownItem(text: '选项1', value: 1, disabled: true, iconData: Icons.settings),
                    DropdownItem(text: '选项2', value: 2, iconData: Icons.settings, divided: true),
                    DropdownItem(text: '选项', value: 3, iconData: IconFont.mianxing_huibao),
                    DropdownItem(text: '选', value: 4),
                    DropdownItem(text: '选选', value: 5),
                    DropdownItem(text: '选选选', value: 6),
                    DropdownItem(text: '选选选选', value: 7),
                    DropdownItem(text: '选选选选选', value: 8),
                    DropdownItem(text: '选选选选选选选', value: 9),
                  ],
                  onPressed: () {
                    print('点击了按钮');
                  },
                  onItemSelected: (item) {
                    print('object-----');

                    print('选择了: ${item.text}, 值: ${item.value}');
                  },
                ),
              ],
            ),

            // Row(
            //   children: [
            //     Wrap(
            //       spacing: 10,
            //       children: [
            //         AppDropdown(
            //           text: '选择选项',
            //           type: DropdownType.default_,
            //           placement: DropdownPlacement.bottomLeft,
            //           size: DropdownSize.small,
            //           // offset: const Offset(0, 8),
            //           trigger: DropdownTrigger.click, // 可选hover或click
            //           items: items,
            //           onPressed: () {
            //             print('点击了按钮');
            //           },
            //           onItemSelected: (item) {
            //             print('object-----');

            //             print('选择了: ${item.text}, 值: ${item.value}');
            //           },
            //         ),
            //         AppDropdown(
            //           text: '选择选项',
            //           type: DropdownType.default_,
            //           placement: DropdownPlacement.bottomCenter,
            //           size: DropdownSize.small,
            //           trigger: DropdownTrigger.click, // 可选hover或click
            //           items: items,
            //           onPressed: () {
            //             print('点击了按钮');
            //           },
            //           onItemSelected: (item) {
            //             print('object-----');

            //             print('选择了: ${item.text}, 值: ${item.value}');
            //           },
            //         ),
            //         AppDropdown(
            //           text: '选择选项',
            //           type: DropdownType.default_,
            //           placement: DropdownPlacement.bottomRight,
            //           size: DropdownSize.small,
            //           // offset: const Offset(0, 8),
            //           trigger: DropdownTrigger.click, // 可选hover或click
            //           items: items,
            //           onPressed: () {
            //             print('点击了按钮');
            //           },
            //           onItemSelected: (item) {
            //             print('object-----');

            //             print('选择了: ${item.text}, 值: ${item.value}');
            //           },
            //         ),
            //       ],
            //     ),
            //   ],
            // ),

            // Row(
            //   children: [
            //     Wrap(
            //       spacing: 10,
            //       children: [
            //         AppDropdown(
            //           text: '选择选项',
            //           type: DropdownType.default_,
            //           placement: DropdownPlacement.topLeft,
            //           size: DropdownSize.small,
            //           // offset: const Offset(0, 8),
            //           trigger: DropdownTrigger.click, // 可选hover或click
            //           items: items,
            //           onPressed: () {
            //             print('点击了按钮');
            //           },
            //           onItemSelected: (item) {
            //             print('object-----');

            //             print('选择了: ${item.text}, 值: ${item.value}');
            //           },
            //         ),
            //         AppDropdown(
            //           text: '选择选项',
            //           type: DropdownType.default_,
            //           placement: DropdownPlacement.topCenter,
            //           size: DropdownSize.small,
            //           // offset: const Offset(0, 8),
            //           trigger: DropdownTrigger.click, // 可选hover或click
            //           items: items,
            //           onPressed: () {
            //             print('点击了按钮');
            //           },
            //           onItemSelected: (item) {
            //             print('object-----');

            //             print('选择了: ${item.text}, 值: ${item.value}');
            //           },
            //         ),
            //         AppDropdown(
            //           text: '选择选项',
            //           type: DropdownType.default_,
            //           placement: DropdownPlacement.topRight,
            //           size: DropdownSize.small,
            //           // offset: const Offset(0, 8),
            //           trigger: DropdownTrigger.click, // 可选hover或click
            //           items: items,
            //           onPressed: () {
            //             print('点击了按钮');
            //           },
            //           onItemSelected: (item) {
            //             print('object-----');

            //             print('选择了: ${item.text}, 值: ${item.value}');
            //           },
            //         ),
            //       ],
            //     ),
            //   ],
            // ),

            // Row(
            //   spacing: 50,
            //   children: [
            //     AppDropdown(
            //       text: '选择选项',
            //       placement: DropdownPlacement.topLeft,
            //       // type: DropdownType.default_,
            //       size: DropdownSize.small,
            //       // offset: const Offset(0, 8),
            //       trigger: DropdownTrigger.click, // 可选hover或click
            //       items: items2,
            //       onPressed: () {
            //         print('点击了按钮');
            //       },
            //       onItemSelected: (item) {
            //         print('object-----');

            //         print('选择了: ${item.text}, 值: ${item.value}');
            //       },
            //       child: Text('12b'),
            //     ),
            //   ],
            // ),

            // Row(
            //   spacing: 50,
            //   children: [
            //     AppDropdown(
            //       size: DropdownSize.mini,
            //       items: items2,
            //       text: '选择选项',
            //       trigger: DropdownTrigger.click, // 可选hover或click
            //       child: AppButton(
            //         iconData: Icons.star_rounded,
            //         size: ButtonSize.small,
            //         color: AppColors.yellow,
            //         type: ButtonType.transparent,
            //         onPressed: () {},
            //       ),
            //     ),

            //     AppDropdown(
            //       size: DropdownSize.small,
            //       items: items2,
            //       text: '选择选项',
            //       trigger: DropdownTrigger.click, // 可选hover或click
            //       child: AppButton(
            //         iconData: Icons.star_rounded,
            //         size: ButtonSize.small,
            //         color: AppColors.yellow,
            //         type: ButtonType.transparent,
            //         onPressed: () {},
            //       ),
            //     ),

            //     AppDropdown(
            //       size: DropdownSize.medium,
            //       items: items2,
            //       text: '选择选项',
            //       trigger: DropdownTrigger.click, // 可选hover或click
            //       child: AppButton(
            //         iconData: Icons.star_rounded,
            //         size: ButtonSize.small,
            //         color: AppColors.yellow,
            //         type: ButtonType.transparent,
            //         onPressed: () {},
            //       ),
            //     ),
            //     AppDropdown(
            //       size: DropdownSize.large,
            //       items: items2,
            //       text: '选择选项',
            //       trigger: DropdownTrigger.click, // 可选hover或click
            //       child: AppButton(
            //         iconData: Icons.star_rounded,
            //         size: ButtonSize.small,
            //         color: AppColors.yellow,
            //         type: ButtonType.transparent,
            //         onPressed: () {},
            //       ),
            //     ),
            //   ],
            // ),

            // Row(
            //   children: [
            //     Wrap(
            //       spacing: 50,
            //       children: [
            //         AppDropdown(
            //           items: items2,
            //           text: '选择选项',
            //           trigger: DropdownTrigger.hover, // 可选hover或click
            //           onPressed: () {
            //             print('点击了按钮');
            //           },
            //           onItemSelected: (item) {
            //             print('object-----');
            //             print('选择了: ${item.text}, 值: ${item.value}');
            //           },
            //           child: AppButton(
            //             iconData: Icons.star_rounded,
            //             size: ButtonSize.small,
            //             color: AppColors.yellow,
            //             type: ButtonType.transparent,
            //             onPressed: () {},
            //           ),
            //         ),

            //         AppDropdown(
            //           items: items2,
            //           text: '选择选项',
            //           trigger: DropdownTrigger.click, // 可选hover或click
            //           child: AppButton(
            //             iconData: Icons.star_rounded,
            //             size: ButtonSize.small,
            //             color: AppColors.yellow,
            //             type: ButtonType.transparent,
            //             onPressed: () {},
            //           ),
            //         ),

            //         // 基本手动控制
            //         // AppCustomTooltip(
            //         //   manual: true,
            //         //   isShow: isShowTooltip1,
            //         //   content: '手动控制的提示框',
            //         //   contentBuilder: (context, closeTooltip) {
            //         //     return Column(
            //         //       mainAxisSize: MainAxisSize.min,
            //         //       crossAxisAlignment: CrossAxisAlignment.start,
            //         //       children: [
            //         //         for (int i = 0; i < items2.length; i++)
            //         //           Material(
            //         //             color: Colors.transparent,
            //         //             child: InkWell(
            //         //               onTap:
            //         //                   items2[i].disabled
            //         //                       ? null
            //         //                       : () {
            //         //                         print('选择了: ${items2[i].text}, 值: ${items2[i].value}');
            //         //                         // 操作完成后关闭tooltip
            //         //                         closeTooltip();
            //         //                       },
            //         //               child: Container(
            //         //                 width: 150,
            //         //                 padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            //         //                 child: Row(
            //         //                   children: [
            //         //                     if (items2[i].iconData != null) ...[
            //         //                       Icon(
            //         //                         items2[i].iconData,
            //         //                         size: 16,
            //         //                         color: items2[i].disabled ? Colors.grey : null,
            //         //                       ),
            //         //                       SizedBox(width: 8),
            //         //                     ],
            //         //                     if (items2[i].svgName != null) ...[
            //         //                       // 这里可以添加SVG图标
            //         //                       SizedBox(width: 8),
            //         //                     ],
            //         //                     Expanded(
            //         //                       child: Text(
            //         //                         items2[i].text,
            //         //                         style: TextStyle(
            //         //                           color: items2[i].disabled ? Colors.grey : null,
            //         //                         ),
            //         //                       ),
            //         //                     ),
            //         //                   ],
            //         //                 ),
            //         //               ),
            //         //             ),
            //         //           ),
            //         //         if (items2.isNotEmpty)
            //         //           Padding(
            //         //             padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            //         //             child: ElevatedButton(
            //         //               onPressed: () {
            //         //                 print('添加了一个自定义按钮');
            //         //                 closeTooltip();
            //         //               },
            //         //               child: Text('自定义操作'),
            //         //             ),
            //         //           ),
            //         //       ],
            //         //     );
            //         //   },
            //         //   child: ElevatedButton(
            //         //     onPressed: () {
            //         //       setState(() {
            //         //         isShowTooltip1 = !isShowTooltip1;
            //         //       });
            //         //     },
            //         //     child: Text(' (${isShowTooltip1 ? "显示中" : "隐藏中"})'),
            //         //   ),
            //         // ),
            //       ],
            //     ),
            //   ],
            // ),
            // Row(
            //   children: [
            //     Wrap(
            //       spacing: 50,
            //       children: [
            //         AppDropdown(
            //           text: '选择选项',
            //           textOnly: true,
            //           type: DropdownType.default_,
            //           placement: DropdownPlacement.bottomLeft,
            //           size: DropdownSize.small,
            //           // offset: const Offset(0, 8),
            //           trigger: DropdownTrigger.click, // 可选hover或click
            //           items: items2,
            //           onPressed: () {
            //             print('点击了按钮');
            //           },
            //           onItemSelected: (item) {
            //             print('object-----');

            //             print('选择了: ${item.text}, 值: ${item.value}');
            //           },
            //         ),
            //         AppDropdown(
            //           text: '选择选项',
            //           textOnly: true,
            //           type: DropdownType.primary,
            //           placement: DropdownPlacement.bottomLeft,
            //           size: DropdownSize.small,
            //           // offset: const Offset(0, 8),
            //           trigger: DropdownTrigger.click, // 可选hover或click
            //           items: items2,
            //           onPressed: () {
            //             print('点击了按钮');
            //           },
            //           onItemSelected: (item) {
            //             print('object-----');

            //             print('选择了: ${item.text}, 值: ${item.value}');
            //           },
            //         ),
            //       ],
            //     ),
            //   ],
            // ),
            // Row(
            //   children: [
            //     Wrap(
            //       spacing: 50,
            //       children: [
            //         AppDropdown(
            //           text: '选择选项',
            //           type: DropdownType.default_,
            //           placement: DropdownPlacement.topCenter,
            //           size: DropdownSize.small,
            //           // offset: const Offset(0, 8),
            //           trigger: DropdownTrigger.click, // 可选hover或click
            //           items: items2,
            //           onPressed: () {
            //             print('点击了按钮');
            //           },
            //           onItemSelected: (item) {
            //             print('object-----');

            //             print('选择了: ${item.text}, 值: ${item.value}');
            //           },
            //         ),

            //         AppDropdown(
            //           text: '选择选项',
            //           type: DropdownType.primary,
            //           placement: DropdownPlacement.topCenter,
            //           size: DropdownSize.small,
            //           // offset: const Offset(0, 8),
            //           trigger: DropdownTrigger.click, // 可选hover或click
            //           items: items2,
            //           onPressed: () {
            //             print('点击了按钮');
            //           },
            //           onItemSelected: (item) {
            //             print('object-----');

            //             print('选择了: ${item.text}, 值: ${item.value}');
            //           },
            //         ),
            //       ],
            //     ),
            //   ],
            // ),
          ],
        ),
      ),
    );
  }
}
