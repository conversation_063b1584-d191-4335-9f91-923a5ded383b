import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:octasync_client/views/projects/components/projects_tab/tabs/tabs_provider.dart';
import 'package:octasync_client/views/projects/components/projects_tab/trunk_item.dart';
import 'package:provider/provider.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class ProjectsTabDashboard extends StatefulWidget {
  final GlobalKey<AppTabsState> tabsKey;

  const ProjectsTabDashboard({super.key, required this.tabsKey});

  @override
  State<ProjectsTabDashboard> createState() => _ProjectsTabDashboardState();
}

class _ProjectsTabDashboardState extends State<ProjectsTabDashboard> {
  // 声明为非final，允许在initState中赋值
  final ScrollController _scrollController = ScrollController();

  // 用于垂直滚动条的控制器列表
  final List<ScrollController> _verticalScrollControllers = [];

  // 在类成员中添加可见性跟踪变量
  // 用于跟踪当前已经被要求渲染的项目索引
  final Set<int> _requestedRenderIndexes = {};

  int flag = 100;
  final double blockTitleHeight = 44;

  @override
  void dispose() {
    // 释放所有垂直滚动控制器
    for (var controller in _verticalScrollControllers) {
      controller.dispose();
    }
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var borderColor = Variables.commonBorderColor(context);

    // 假设这里有一个动态列表，表示内容块的数据
    final List<Map<String, dynamic>> contentBlocks = [
      {'color': const Color.fromARGB(255, 255, 201, 201), 'title': '规划中', 'total': 108},
      {'color': const Color.fromARGB(255, 167, 186, 232), 'title': '正常', 'total': 10},
      {'color': const Color.fromARGB(255, 222, 161, 161), 'title': '滞后', 'total': 5},
      // {'color': const Color.fromARGB(255, 186, 168, 220), 'title': '其他1', 'total': 5},
      // {'color': const Color.fromARGB(255, 175, 191, 205), 'title': '其他2', 'total': 5},
      // {'color': const Color.fromARGB(255, 204, 153, 133), 'title': '其他3', 'total': 5},
    ];

    // 计算内容块所需的总宽度
    final int blockCount = contentBlocks.length;
    final double blockWidth = 400.0; // 每个内容块的固定宽度
    // + blockWidth 最后有一列操作列
    final double blocksWidth = blockCount * blockWidth + blockWidth;

    return LayoutBuilder(
      builder: (context, constraints) {
        // 获取屏幕宽度（父容器的最大宽度）
        final screenWidth = constraints.maxWidth;

        // 计算实际需要的内容宽度 - 基于内容块数量动态计算
        final double baseWidth = 800.0; // 基础UI宽度，包括按钮区等
        final double minContentWidth = math.max(baseWidth, blocksWidth);

        // 内容页宽度：如果屏幕宽度大于内容最小宽度，则使用屏幕宽度，否则使用最小宽度
        final contentWidth = math.max(screenWidth, minContentWidth);

        // 是否需要水平滚动：只有当内容宽度超过屏幕宽度时才需要
        final needsHorizontalScrolling = contentWidth > screenWidth;

        return Scrollbar(
          controller: _scrollController,
          thumbVisibility: needsHorizontalScrolling,
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            controller: _scrollController,
            child: SizedBox(
              width: contentWidth,
              child: Container(
                width: contentWidth,
                color: context.background100,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 使用Row + 抽离的构建方法
                    Expanded(
                      child: Container(
                        color: context.background100,
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 使用IndexedBuilder模式，避免一次性创建所有组件
                            ...contentBlocks
                                .asMap()
                                .entries
                                .map(
                                  (entry) => _buildContentBlock(
                                    entry.value,
                                    entry.key,
                                    blockWidth,
                                    borderColor,
                                  ),
                                )
                                .toList(),

                            // 操作区域块
                            _buildActionBlock(blockWidth),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // 优化内容块的构建方法，抽取为单独的方法以便复用
  Widget _buildContentBlock(
    Map<String, dynamic> block,
    int index,
    double blockWidth,
    Color borderColor,
  ) {
    // 懒加载：只有在首次需要时才创建控制器
    if (_verticalScrollControllers.length <= index) {
      while (_verticalScrollControllers.length <= index) {
        _verticalScrollControllers.add(ScrollController());
      }
    }

    // 为每个垂直滚动控制器添加监听器，检测滚动位置变化
    final controller = _verticalScrollControllers[index];
    if (!controller.hasListeners) {
      controller.addListener(() {
        // 滚动时触发渲染请求检查
        _checkVisibleItemsForRendering(controller, index);
      });
    }

    return SizedBox(
      width: blockWidth, // 强制固定宽度
      child: Container(
        decoration: BoxDecoration(
          border: Border(right: BorderSide(color: borderColor, width: 1.0)),
        ),
        // 确保容器填充整个高度
        height: double.infinity,
        child: Column(
          children: [
            // 固定高度的title块
            Container(
              margin: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              height: blockTitleHeight, // 固定高度
              width: double.infinity,
              decoration: BoxDecoration(
                color: block['color'],
                borderRadius: BorderRadius.all(Radius.circular(AppRadiusSize.radius4)), // 圆角
              ),
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              alignment: Alignment.centerLeft,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(block['title'], style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                  SizedBox(width: 4),
                  AppBadge(value: block['total']),
                ],
              ),
            ),

            // 内容区域（带滚动条）- 使用ListView.builder替代SingleChildScrollView + Column
            Expanded(
              // 使用NotificationListener和ValueKey优化滚动性能
              child: NotificationListener<ScrollNotification>(
                onNotification: (notification) {
                  // 高级滚动检测
                  if (notification is ScrollUpdateNotification) {
                    // 滚动更新时触发检查
                    _checkVisibleItemsForRendering(_verticalScrollControllers[index], index);
                  } else if (notification is ScrollEndNotification) {
                    // 滚动结束时再次检查，确保所有需要的项目都被渲染
                    _checkVisibleItemsForRendering(_verticalScrollControllers[index], index);
                  }
                  return false; // 继续向上传递通知
                },
                child: Scrollbar(
                  controller: _verticalScrollControllers[index],
                  thumbVisibility: true,
                  child: ListView.builder(
                    key: ValueKey("content_list_$index"), // 使用唯一key避免重建
                    controller: _verticalScrollControllers[index],
                    padding: EdgeInsets.only(left: 15, right: 15, bottom: 10),
                    // 使用builder模式，只渲染可见项
                    itemCount: 10, // 假设有10个项目
                    itemBuilder: (context, i) {
                      // 高级延迟加载策略:
                      // 1. 首屏项目(前6个)始终立即渲染
                      // 2. 已请求渲染的项目立即渲染(通过滚动触发)
                      // 3. 其余项目应用渐进延迟，但有最大延迟上限
                      if (i > 5 && !_requestedRenderIndexes.contains(i)) {
                        // 计算延迟时间，但设置上限为2000ms
                        int delayTime = math.min(200 * (i - 5), 2000);

                        return FutureBuilder(
                          future: Future.delayed(Duration(milliseconds: delayTime)),
                          builder: (context, snapshot) {
                            if (snapshot.connectionState == ConnectionState.done) {
                              return _buildTrunkItem(i);
                            } else {
                              return Container(
                                height: 80, // 与TrunkItem高度一致
                                margin: EdgeInsets.only(top: i == 0 ? 0 : 10),
                                decoration: BoxDecoration(
                                  color: context.background300,
                                  borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
                                ),
                              );
                            }
                          },
                        );
                      }

                      // 前六个或已请求渲染的项目立即渲染
                      return _buildTrunkItem(i);
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 抽取TrunkItem构建逻辑
  Widget _buildTrunkItem(int index) {
    return Container(
      margin: EdgeInsets.only(top: index == 0 ? 0 : 10, bottom: 0),
      // child: TrunkItem(),
      child: Text('测试~~~~~~~~~~~~~~~~~~'),
    );
  }

  // 优化操作区块的构建
  Widget _buildActionBlock(double blockWidth) {
    return SizedBox(
      width: blockWidth,
      child: Container(
        height: double.infinity,
        child: Column(
          children: [
            Expanded(
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 15),
                padding: EdgeInsets.all(8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextButton(
                      onPressed: () {
                        widget.tabsKey.currentState?.scrollToTab(1);
                      },
                      child: Text('移动到第2个'),
                    ),
                    Builder(
                      builder: (context) {
                        return TextButton(
                          onPressed: () {
                            flag++;
                            context.read<TabsProvider>().appendItem(
                              TabItem(id: flag, title: 'Title_$flag'),
                            );
                            context.read<TabsProvider>().changeCheckedId(flag);

                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              widget.tabsKey.currentState?.scrollToTab(flag);
                            });
                          },
                          child: Text('追加并且移动到最新节点'),
                        );
                      },
                    ),

                    TrunkItem(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 根据滚动位置检查并触发渲染请求
  void _checkVisibleItemsForRendering(ScrollController controller, int blockIndex) {
    if (!mounted) return;

    // 获取当前滚动位置
    final scrollPosition = controller.position;
    final viewportHeight = scrollPosition.viewportDimension;
    final scrollOffset = scrollPosition.pixels;

    // 计算可见区域范围 (添加一定的预加载区域)
    final double preloadDistance = viewportHeight; // 预加载一屏的距离
    final double startVisible = math.max(0, scrollOffset - preloadDistance);
    final double endVisible = scrollOffset + viewportHeight + preloadDistance;

    // 估算每个项目的高度，包括间距 (TrunkItem高度 + 间距)
    const double estimatedItemHeight = 80 + 10;

    // 计算可能在可视区域内的项目索引范围
    final int startIndex = math.max(0, (startVisible / estimatedItemHeight).floor());
    final int endIndex = math.min(9, (endVisible / estimatedItemHeight).ceil()); // 假设最大10个项目

    // 向state发送更新请求，触发这些项目的立即渲染
    setState(() {
      for (int i = startIndex; i <= endIndex; i++) {
        _requestedRenderIndexes.add(i);
      }
    });
  }
}
