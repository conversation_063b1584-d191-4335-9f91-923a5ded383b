import 'package:octasync_client/utils/http_service.dart';

final _http = HttpService();

const controller = '/Business/Job/';

/// 企业信息配置
class JobApi {
  static Future<dynamic> add(data) {
    return _http.post('${controller}Add', data: data);
  }

  static Future<dynamic> edit(data) {
    return _http.post('${controller}Edit', data: data);
  }

  static Future<dynamic> delete(data) {
    return _http.post('${controller}Delete', data: data);
  }

  static Future<dynamic> getListPage(data) {
    return _http.post('${controller}GetListPage', data: data);
  }

  static Future<dynamic> getDetails(data) {
    return _http.post('${controller}GetDetails', data: data);
  }
}
