import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_checkbox.dart';

class CheckboxCell extends StatefulWidget {
  const CheckboxCell({
    super.key,
    required this.column,
    required this.rowData,
    required this.rowIdx,
    required this.columnIdx,
    required this.rowId,
    required this.columnId,
  });

  final AppTableColumn column;

  final Map<String, dynamic> rowData;

  final int rowIdx;

  final int columnIdx;

  final String rowId;
  final String columnId;

  @override
  State<CheckboxCell> createState() => _CheckboxCellState();
}

class _CheckboxCellState extends State<CheckboxCell> {
  late AppTableColumn column;
  late AppTableColumnTypeCheckbox columnObj;
  late Map<String, dynamic> rowData;
  late String rowId;
  late String columnId;

  bool? _isChecked = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    column = widget.column;
    columnObj = column.type as AppTableColumnTypeCheckbox;
    rowData = widget.rowData;
    rowId = widget.rowId;
    columnId = widget.columnId;

    var value = rowData[column.field];
    if (value is bool) {
      _isChecked = value;
    } else if (value is String) {
      _isChecked = bool.tryParse(value.toString());
    } else if (value == null) {
      _isChecked = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Checkbox(
      value: _isChecked,
      onChanged: (value) {
        setState(() {
          _isChecked = value!;
        });
        rowData[column.field] = value!.toString();
      },
    );
  }
}
