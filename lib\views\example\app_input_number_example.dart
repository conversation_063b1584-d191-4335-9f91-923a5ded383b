import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// AppInputNumber 组件使用示例
class AppInputNumberExample extends StatefulWidget {
  const AppInputNumberExample({super.key});

  @override
  State<AppInputNumberExample> createState() => _AppInputNumberExampleState();
}

class _AppInputNumberExampleState extends State<AppInputNumberExample> {
  double _basicValue = 0;
  double _sizeValue = 0;
  double _rangeValue = 5;
  double _stepValue = 0;
  double _precisionValue = 1.5;
  double _verticalValue = 10;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('InputNumber 组件示例')),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 基础用法
              _buildSection(
                title: '基础用法',
                description: '样式已分离到独立文件，代码结构更清晰，便于维护',
                child: AppInputNumber(
                  value: _basicValue,
                  onChanged: (value) {
                    print('基础用法: $value');
                    setState(() {
                      _basicValue = value;
                    });
                  },
                ),
              ),
              // 尺寸用法
              _buildSection(
                title: '尺寸用法',
                description: '不同的宽度和大小',
                child: SizedBox(
                  width: 200,
                  child: AppInputNumber(
                    value: _sizeValue,
                    size: InputNumberSize.medium,
                    onChanged: (value) {
                      setState(() {
                        _sizeValue = value;
                      });
                    },
                  ),
                ),
              ),

              // 设置范围
              _buildSection(
                title: '设置数值范围',
                description: '限制输入范围在 1-10 之间',
                child: AppInputNumber(
                  value: _rangeValue,
                  min: 1,
                  max: 10,
                  onChanged: (value) {
                    print('设置数值范围: $value');
                    setState(() {
                      _rangeValue = value;
                    });
                  },
                ),
              ),

              // 步长设置
              _buildSection(
                title: '步长设置',
                description: '每次增减 0.1',
                child: AppInputNumber(
                  value: _stepValue,
                  step: 0.1,
                  precision: 1,
                  min: -2,
                  max: 2,
                  onChanged: (value) {
                    print('步长设置: $value');

                    setState(() {
                      _stepValue = value;
                    });
                  },
                ),
              ),

              // 精度设置
              _buildSection(
                title: '精度设置',
                description: '保留两位小数',
                child: AppInputNumber(
                  value: _precisionValue,
                  step: 0.01,
                  precision: 2,
                  onChanged: (value) {
                    print('精度设置: $value');
                    setState(() {
                      _precisionValue = value;
                    });
                  },
                ),
              ),

              // 垂直布局
              _buildSection(
                title: '垂直按钮布局',
                description: '按钮在右侧垂直排列',
                child: AppInputNumber(
                  value: _verticalValue,
                  controlsPosition: 'right',
                  width: 120,
                  onChanged: (value) {
                    print('垂直按钮布局: $value');
                    setState(() {
                      _verticalValue = value;
                    });
                  },
                ),
              ),

              // 禁用状态
              _buildSection(
                title: '禁用状态',
                description: '组件处于禁用状态，边框和背景色自动调整',
                child: const AppInputNumber(value: 5, disabled: true),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String description,
    required Widget child,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Text(description, style: TextStyle(fontSize: 12, color: Colors.grey.shade600)),
          const SizedBox(height: 8),
          child,
        ],
      ),
    );
  }
}
