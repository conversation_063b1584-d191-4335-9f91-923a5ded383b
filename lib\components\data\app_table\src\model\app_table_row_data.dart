import 'package:json_annotation/json_annotation.dart';

part 'app_table_row_data.g.dart';

@JsonSerializable()
class AppTableRowData {
  Map<String, dynamic> data;
  int level;
  bool hasChildren;
  bool isExpanded;
  String? parentId;
  final String id;
  List<AppTableRowData>? children;
  final int? levelOneIndex; // 如果是树形表格，该索引只有一级节点才有，其他子节点该字段为null
  int childrenCount; // 当前节点的一级（直接）子节点数量
  int descendantsCount; // 所有层级子节点总数
  bool isLeaf;
  bool isChecked; //是否选中行（表格单选、多选）

  AppTableRowData({
    required this.data,
    required this.level,
    required this.hasChildren,
    this.isExpanded = false,
    required this.parentId,
    required this.id,
    this.children,
    this.levelOneIndex,
    this.childrenCount = 0,
    this.descendantsCount = 0,
    this.isLeaf = true,
    this.isChecked = false,
  });

  factory AppTableRowData.fromJson(Map<String, dynamic> json) =>
      _$AppTableRowDataFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AppTableRowDataToJson(this);
}
