import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class UserProvider extends ChangeNotifier {
  UserInfo? _userInfo;
  bool _isLogin = false;

  UserInfo? get userInfo => _userInfo;
  bool get isLogin => _isLogin;

  UserProvider() {
    _initUserInfo();
  }

  // 初始化用户信息
  Future<void> _initUserInfo() async {
    _userInfo = await StorageUtil.getUserInfo();
    _isLogin = await StorageUtil.getLoginStatus();
    notifyListeners();
  }

  // 设置用户信息
  Future<void> setUserInfo(UserInfo userInfo) async {
    _userInfo = userInfo;
    _isLogin = true;

    // 保存到本地存储
    await StorageUtil.setUserInfo(userInfo);
    await StorageUtil.setLoginStatus(true);
    await StorageUtil.setToken(userInfo.token!);

    notifyListeners();
  }

  // 清除用户信息
  Future<void> clearUserInfo() async {
    _userInfo = null;
    _isLogin = false;

    // 清除本地存储
    await StorageUtil.removeUserInfo();
    await StorageUtil.removeLoginStatus();
    await StorageUtil.removeToken();

    print('清空用户信息');

    notifyListeners();
  }
}
