import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

/// AppFormField 组件示例页面
class FormFieldExamplePage extends StatefulWidget {
  const FormFieldExamplePage({super.key});

  @override
  State<FormFieldExamplePage> createState() => _FormFieldExamplePageState();
}

class _FormFieldExamplePageState extends State<FormFieldExamplePage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final SelectController<int> _gradeController = SelectController<int>();
  final SelectController<int> _sexController = SelectController<int>();
  final TextEditingController _descriptionController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('FormField 组件示例')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionTitle('Builder 模式'),
              _buildDescription(
                '适用于 AppSelect 等需要手动触发验证的组件,Form设置autovalidateMode: AutovalidateMode.onUserInteraction',
              ),

              // 职级选择（builder 模式）
              AppFormField(
                label: '职级序列',
                required: true,
                labelWidth: 80,
                labelPosition: FormFieldLabelPosition.left,
                validator: (value) {
                  if (_gradeController.value == null) {
                    return '请选择职级序列';
                  }
                  return null;
                },
                builder:
                    (field) => AppSelect<int>(
                      placeholder: '请选择职级序列',
                      options: const [
                        SelectOption(value: 1, label: '管理岗位'),
                        SelectOption(value: 2, label: '软/硬件研发'),
                        SelectOption(value: 3, label: '营销类'),
                        SelectOption(value: 4, label: '行政后勤'),
                      ],
                      controller: _gradeController,
                      onChanged: (value) {
                        field.didChange(value); // 重要：触发验证
                      },
                    ),
              ),

              const SizedBox(height: 16),

              // 示例4：性别选择（builder 模式）
              AppFormField(
                label: '性别',
                required: true,
                labelWidth: 80,
                labelPosition: FormFieldLabelPosition.left,
                validator: (value) {
                  if (_sexController.value == null) {
                    return '请选择性别';
                  }
                  return null;
                },
                builder:
                    (field) => AppSelect<int>(
                      placeholder: '请选择性别',
                      options: const [
                        SelectOption(value: 1, label: '男'),
                        SelectOption(value: 2, label: '女'),
                        SelectOption(value: 3, label: '保密'),
                      ],
                      controller: _sexController,
                      onChanged: (value) {
                        field.didChange(value); // 重要：触发验证
                      },
                    ),
              ),

              const SizedBox(height: 32),

              _buildSectionTitle('上方标签布局'),
              _buildDescription('标签位置在表单控件上方'),

              // 示例5：职责说明（上方标签）
              AppFormField(
                label: '职责说明',
                labelPosition: FormFieldLabelPosition.top,
                child: AppInput(
                  controller: _descriptionController,
                  hintText: '请输入职责说明',
                  maxLines: 5,
                  maxLength: 500,
                ),
              ),

              const SizedBox(height: 32),

              // 操作按钮
              Row(
                children: [
                  AppButton(
                    text: '验证表单',
                    type: ButtonType.primary,
                    onPressed: () {
                      if (_formKey.currentState?.validate() ?? false) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('表单验证通过！'),
                            backgroundColor: AppColors.success,
                          ),
                        );
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('请检查表单输入'),
                            backgroundColor: AppColors.error,
                          ),
                        );
                      }
                    },
                  ),
                  const SizedBox(width: 16),
                  AppButton(
                    text: '重置表单',
                    type: ButtonType.default_,
                    onPressed: () {
                      _nameController.clear();
                      _emailController.clear();
                      _gradeController.clear();
                      _sexController.clear();
                      _descriptionController.clear();
                      _formKey.currentState?.reset();
                    },
                  ),
                ],
              ),

              const SizedBox(height: 32),

              _buildSectionTitle('使用说明'),
              _buildCodeExample(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
    );
  }

  Widget _buildDescription(String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(description, style: TextStyle(fontSize: 14, color: Colors.grey[600])),
    );
  }

  Widget _buildCodeExample() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('两种使用模式：', style: TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(height: 8),
          const Text('1. Child 模式（适用于 AppInput）：', style: TextStyle(fontWeight: FontWeight.w500)),
          const SizedBox(height: 4),
          Text(
            'AppFormField(\n'
            '  label: "姓名",\n'
            '  required: true,\n'
            '  validator: (value) => ...,\n'
            '  child: AppInput(...),\n'
            ')',
            style: TextStyle(fontFamily: 'monospace', fontSize: 12, color: Colors.grey[700]),
          ),
          const SizedBox(height: 16),
          const Text(
            '2. Builder 模式（适用于 AppSelect）：',
            style: TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 4),
          Text(
            'AppFormField(\n'
            '  label: "性别",\n'
            '  required: true,\n'
            '  validator: (value) => ...,\n'
            '  builder: (field) => AppSelect(\n'
            '    onChanged: (value) {\n'
            '      field.didChange(value); // 触发验证\n'
            '    },\n'
            '  ),\n'
            ')',
            style: TextStyle(fontFamily: 'monospace', fontSize: 12, color: Colors.grey[700]),
          ),
        ],
      ),
    );
  }
}
