import 'package:json_annotation/json_annotation.dart';

part 'employee_tree_model.g.dart';

/// 员工树形结构数据模型
/// 用于表示部门和员工的层级关系
@JsonSerializable(explicitToJson: true, includeIfNull: false, fieldRename: FieldRename.pascal)
class EmployeeTreeModel {
  /// 唯一标识符
  @Json<PERSON>ey(name: 'Id')
  final String? id;

  /// 头像URL
  @JsonKey(name: 'Avatar')
  final String? avatar;

  /// 名称（部门名或员工姓名）
  @JsonKey(name: 'Name', defaultValue: '')
  final String name;

  /// 父级ID
  @JsonKey(name: 'ParentId')
  final String? parentId;

  /// 父级名称
  @JsonKey(name: 'ParentName', defaultValue: '')
  final String parentName;

  /// 类型：1-部门，2-员工
  @JsonKey(name: 'Type', defaultValue: 1)
  final int type;

  /// 子节点列表（可选，用于树形结构）
  @Json<PERSON>ey(name: 'Children', defaultValue: <EmployeeTreeModel>[])
  final List<EmployeeTreeModel> children;

  /// 是否展开（UI状态，不参与序列化）
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool isExpanded;

  /// 是否选中（UI状态，不参与序列化）
  @JsonKey(includeFromJson: false, includeToJson: false)
  final bool isSelected;

  const EmployeeTreeModel({
    this.id,
    this.avatar,
    this.name = '',
    this.parentId,
    this.parentName = '',
    this.type = 1,
    this.children = const <EmployeeTreeModel>[],
    this.isExpanded = false,
    this.isSelected = false,
  });

  /// 从JSON创建实例
  factory EmployeeTreeModel.fromJson(Map<String, dynamic> json) {
    return _$EmployeeTreeModelFromJson(json);
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$EmployeeTreeModelToJson(this);

  /// 从JSON列表创建实例列表
  static List<EmployeeTreeModel> fromJsonList(List<dynamic>? jsonList) {
    if (jsonList == null) return <EmployeeTreeModel>[];
    return jsonList
        .map((json) => EmployeeTreeModel.fromJson(json as Map<String, dynamic>))
        .toList();
  }

  /// 转换为JSON列表
  static List<Map<String, dynamic>> toJsonList(List<EmployeeTreeModel> models) {
    return models.map((model) => model.toJson()).toList();
  }

  /// 检查是否为部门
  bool get isDepartment => type == 1;

  /// 检查是否为员工
  bool get isEmployee => type == 2;

  /// 是否有子节点
  bool get hasChildren => children.isNotEmpty;

  /// 获取完整路径名称（包含父级名称）
  String get fullName {
    if (parentName.isNotEmpty) {
      return '$parentName - $name';
    }
    return name;
  }

  /// 复制并修改属性
  EmployeeTreeModel copyWith({
    String? id,
    String? avatar,
    String? name,
    String? parentId,
    String? parentName,
    int? type,
    List<EmployeeTreeModel>? children,
    bool? isExpanded,
    bool? isSelected,
  }) {
    return EmployeeTreeModel(
      id: id ?? this.id,
      avatar: avatar ?? this.avatar,
      name: name ?? this.name,
      parentId: parentId ?? this.parentId,
      parentName: parentName ?? this.parentName,
      type: type ?? this.type,
      children: children ?? this.children,
      isExpanded: isExpanded ?? this.isExpanded,
      isSelected: isSelected ?? this.isSelected,
    );
  }

  /// 递归查找指定ID的节点
  EmployeeTreeModel? findById(String targetId) {
    if (id == targetId) return this;

    for (final child in children) {
      final found = child.findById(targetId);
      if (found != null) return found;
    }

    return null;
  }

  /// 获取所有叶子节点（员工）
  List<EmployeeTreeModel> get allEmployees {
    final List<EmployeeTreeModel> employees = [];

    if (isEmployee) {
      employees.add(this);
    }

    for (final child in children) {
      employees.addAll(child.allEmployees);
    }

    return employees;
  }

  /// 获取所有部门节点
  List<EmployeeTreeModel> get allDepartments {
    final List<EmployeeTreeModel> departments = [];

    if (isDepartment) {
      departments.add(this);
    }

    for (final child in children) {
      departments.addAll(child.allDepartments);
    }

    return departments;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EmployeeTreeModel && other.id == id && other.name == name && other.type == type;
  }

  @override
  int get hashCode => Object.hash(id, name, type);

  @override
  String toString() {
    return 'EmployeeTreeModel(id: $id, name: $name, type: $type, children: ${children.length})';
  }
}
