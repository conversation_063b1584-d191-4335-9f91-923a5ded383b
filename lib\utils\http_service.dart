import 'dart:io';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/api/employee.dart';
import 'package:octasync_client/models/upload/upload_response.dart';
import 'package:octasync_client/models/http_response.dart';
import 'package:octasync_client/config/config.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class HttpService {
  static final HttpService _instance = HttpService._internal();
  factory HttpService() => _instance;

  late Dio dio;
  static late GlobalKey<NavigatorState> _navigatorKey;

  /// 初始化全局NavigatorKey
  static void init(GlobalKey<NavigatorState> navigatorKey) {
    _navigatorKey = navigatorKey;
  }

  /// 获取全局context
  static BuildContext get globalContext => _navigatorKey.currentContext!;

  // 基础配置
  final BaseOptions _options = BaseOptions(
    baseUrl: AppConfig.BASE_URL,
    connectTimeout: const Duration(seconds: 15),
    receiveTimeout: const Duration(seconds: 15),
    headers: {'Content-Type': 'application/json', 'Accept': 'application/json'},
  );

  HttpService._internal() {
    dio = Dio(_options);
    // 添加拦截器
    dio.interceptors.add(_interceptorsWrapper());
    // 添加日志拦截器
    // dio.interceptors.add(
    //   LogInterceptor(
    //     request: true,
    //     requestHeader: true,
    //     requestBody: true,
    //     responseHeader: true,
    //     responseBody: true,
    //     error: true,
    //   ),
    // );
  }

  // 拦截器
  InterceptorsWrapper _interceptorsWrapper() {
    return InterceptorsWrapper(
      onRequest: (options, handler) async {
        options.headers['Token'] = await StorageUtil.getToken();
        return handler.next(options);
      },
      onResponse: (response, handler) {
        // 在返回响应数据之前做一些处理
        return handler.next(response);
      },
      onError: (DioException e, handler) {
        // 错误处理
        return handler.next(e);
      },
    );
  }

  // HTTP错误状态处理
  Future<dynamic> _handleError(DioException error) async {
    String errorMessage = '未知错误';
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        errorMessage = '连接超时';
        break;
      case DioExceptionType.sendTimeout:
        errorMessage = '请求超时';
        break;
      case DioExceptionType.receiveTimeout:
        errorMessage = '响应超时';
        break;
      case DioExceptionType.badResponse:
        // 服务器响应错误
        int? statusCode = error.response?.statusCode;
        if (statusCode == 401) {
          errorMessage = '未授权，请重新登录';
          EmployeeApi.logOut(globalContext);
        } else if (statusCode == 404) {
          errorMessage = '请求地址不存在';
        } else if (statusCode == 500) {
          errorMessage = '服务器内部错误';
        } else {
          errorMessage = '服务器响应错误: ${error.response?.statusCode}';
        }
        break;
      case DioExceptionType.cancel:
        errorMessage = '请求已取消';
        break;
      case DioExceptionType.connectionError:
        errorMessage = '网络连接错误';
        break;
      case DioExceptionType.badCertificate:
        errorMessage = '证书验证失败';
        break;
      case DioExceptionType.unknown:
        if (error.error is SocketException) {
          errorMessage = '网络连接失败，请检查网络设置';
        } else {
          errorMessage = '未知错误: ${error.message}';
        }
        break;
    }

    // 这里可以添加统一的错误日志或上报
    print('http_service_error: $errorMessage');
    return errorMessage;
  }

  // 执行请求的包装方法
  Future<dynamic> _executeRequest(Future<Response> Function() requestFunc) async {
    try {
      final response = await requestFunc();

      HttpResponse httpResponse = HttpResponse.fromJson(response.data);
      if (httpResponse.messageCode == 1) {
        return httpResponse.tMessageData;
      } else {
        debugPrint('报错信息:${httpResponse.errorMessage}------报错接口: ${response.requestOptions.path}');
        throw Exception(httpResponse.errorMessage ?? '未知错误');
      }
    } on DioException catch (e) {
      final error = await _handleError(e);
      ToastManager.error(error);
      throw Exception(error);
    } catch (e) {
      ToastManager.error(e);
      throw Exception(e);
    }
  }

  // GET请求
  Future<dynamic> get(
    String path, {
    Map<String, dynamic>? params,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onReceiveProgress,
  }) async {
    return _executeRequest(
      () => dio.get(
        path,
        queryParameters: params,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      ),
    );
  }

  // POST请求
  Future<dynamic> post(
    String path, {
    dynamic data,
    Map<String, dynamic>? params,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
    void Function(int, int)? onReceiveProgress,
  }) async {
    return _executeRequest(
      () => dio.post(
        path,
        data: data,
        queryParameters: params,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      ),
    );
  }

  // 下载文件
  Future<dynamic> download(
    String url,
    String savePath, {
    void Function(int, int)? onReceiveProgress,
    Map<String, dynamic>? params,
    CancelToken? cancelToken,
    bool deleteOnError = true,
    String lengthHeader = Headers.contentLengthHeader,
    Options? options,
  }) async {
    return _executeRequest(
      () => dio.download(
        url,
        savePath,
        onReceiveProgress: onReceiveProgress,
        queryParameters: params,
        cancelToken: cancelToken,
        deleteOnError: deleteOnError,
        lengthHeader: lengthHeader,
        options: options,
      ),
    );
  }

  // 上传文件
  Future<UploadResponse> uploadFile(
    PlatformFile fileData, {
    String path = AppConfig.UPLOAD_FILE_URL,
    String fileKey = 'file',
    String? fileName,
    Map<String, dynamic>? params,
    Options? options,
    CancelToken? cancelToken,
    void Function(int, int)? onSendProgress,
  }) async {
    final result = await _executeRequest(() async {
      // 处理二进制数据
      FormData formData = FormData.fromMap({
        fileKey: MultipartFile.fromBytes(fileData.bytes!, filename: fileName ?? fileData.name),
      });

      // 添加其他参数
      if (params != null) {
        params.forEach((key, value) {
          formData.fields.add(MapEntry(key, value.toString()));
        });
      }

      return dio.post(
        path,
        data: formData,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
      );
    });

    return UploadResponse.fromJson(result);
  }
}
