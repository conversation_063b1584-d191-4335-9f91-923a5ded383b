import 'package:flutter/material.dart';
import 'package:octasync_client/views/projects/components/projects_tab/projects_tab_dashboard.dart';
import 'package:octasync_client/views/projects/components/projects_tab/projects_tab_table.dart';
import 'package:octasync_client/views/projects/components/projects_tab/tabs/tabs_provider.dart';
import 'package:octasync_client/views/projects/components/projects_tab/tab_title.dart';

// import 'package:octasync_client/components/models/tab_item.dart';
// import 'package:octasync_client/views/projects/components/projects_tab/trunk_item.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/imports.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class ProjectsTab extends StatefulWidget {
  const ProjectsTab({super.key});

  @override
  State<ProjectsTab> createState() => _ProjectsTabState();
}

class _ProjectsTabState extends State<ProjectsTab> {
  // // 声明为非final，允许在initState中赋值
  // ScrollController _scrollController = ScrollController();

  // // 用于垂直滚动条的控制器列表
  // List<ScrollController> _verticalScrollControllers = [];

  // // 在类成员中添加可见性跟踪变量
  // // 用于跟踪当前已经被要求渲染的项目索引
  // final Set<int> _requestedRenderIndexes = {};

  @override
  void initState() {
    super.initState();
    // 不在这里创建所有垂直控制器，而是按需创建
  }

  // @override
  // void dispose() {
  //   // 释放所有垂直滚动控制器
  //   for (var controller in _verticalScrollControllers) {
  //     controller.dispose();
  //   }
  //   _scrollController.dispose();
  //   super.dispose();
  // }

  /// 性能优化说明：
  /// 1. 页面状态通过父组件的IndexedStack保持，避免重复创建
  /// 2. 懒加载滚动控制器，只为实际需要的内容块创建控制器
  /// 3. 抽取构建方法，减少代码冗余和提高可维护性
  /// 4. 使用 ListView.builder 替代 SingleChildScrollView + Column，实现虚拟化列表
  /// 5. 为前六个项目立即渲染，其余项目延迟渲染，减轻初始加载压力
  /// 6. 使用 ValueKey 避免不必要的重建
  /// 7. 使用 NotificationListener 优化滚动性能

  final double titleHeight = Variables.titleHeight;
  // final _radiusStyle = BorderRadius.circular(AppRadiusSize.radius4);
  int viewModel = 1; // 1=看板视图，2=列表视图
  List<AppToggleButtonItem> buttonList = [
    AppToggleButtonItem(value: 1, label: '看板视图', iconData: IconFont.mianxing_shitu_kapian),
    AppToggleButtonItem(value: 2, label: '列表视图', iconData: IconFont.mianxing_shitu_biaoge),
  ];
  double buttonHeight = Variables.buttonHeight;
  int flag = 100;
  final GlobalKey<AppTabsState> _tabsKey = GlobalKey<AppTabsState>();

  final double blockTitleHeight = 44;

  void _handleTabSelected(BuildContext context, int tabId) {
    print('选中了：$tabId');
    final provider = context.read<TabsProvider>();
    provider.changeCheckedId(tabId);
  }

  void _handleTabClosed(BuildContext context, int tabId) {
    print('关闭了：$tabId');
    final provider = context.read<TabsProvider>();
    provider.removeItem(tabId);
  }

  @override
  Widget build(BuildContext context) {
    // 将TabsProvider放在这里，作为ProjectsTab的私有Provider
    return ChangeNotifierProvider(
      create: (context) => TabsProvider(),
      child: Builder(
        builder: (builderContext) {
          var borderColor = Variables.commonBorderColor(builderContext);

          // 假设这里有一个动态列表，表示内容块的数据
          // final List<Map<String, dynamic>> contentBlocks = [
          //   {'color': const Color.fromARGB(255, 255, 201, 201), 'title': '规划中', 'total': 108},
          //   {'color': const Color.fromARGB(255, 167, 186, 232), 'title': '正常', 'total': 10},
          //   {'color': const Color.fromARGB(255, 222, 161, 161), 'title': '滞后', 'total': 5},
          //   // {'color': const Color.fromARGB(255, 186, 168, 220), 'title': '其他1', 'total': 5},
          //   // {'color': const Color.fromARGB(255, 175, 191, 205), 'title': '其他2', 'total': 5},
          //   // {'color': const Color.fromARGB(255, 204, 153, 133), 'title': '其他3', 'total': 5},
          // ];

          // 计算内容块所需的总宽度
          // final int blockCount = contentBlocks.length;
          // final double blockWidth = 400.0; // 每个内容块的固定宽度
          // + blockWidth 最后有一列操作列
          // final double blocksWidth = blockCount * blockWidth + blockWidth;

          return LayoutBuilder(
            builder: (context, constraints) {
              // 获取屏幕宽度（父容器的最大宽度）
              // final screenWidth = constraints.maxWidth;

              // // 计算实际需要的内容宽度 - 基于内容块数量动态计算
              // final double baseWidth = 800.0; // 基础UI宽度，包括按钮区等
              // final double minContentWidth = math.max(baseWidth, blocksWidth);

              // // 内容页宽度：如果屏幕宽度大于内容最小宽度，则使用屏幕宽度，否则使用最小宽度
              // final contentWidth = math.max(screenWidth, minContentWidth);

              // // 是否需要水平滚动：只有当内容宽度超过屏幕宽度时才需要
              // final needsHorizontalScrolling = contentWidth > screenWidth;

              return Column(
                children: [
                  // 顶部Tabs区域
                  Container(
                    height: titleHeight,
                    decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(color: borderColor, width: 1.0)),
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 5),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TabTitle(),
                          Expanded(
                            // child: Tabs(key: _tabsKey)
                            child: Padding(
                              padding: EdgeInsets.only(top: 5),
                              child: Selector<TabsProvider, ({List<TabItem> items, int currId})>(
                                selector:
                                    (context, provider) => (
                                      items: provider.items,
                                      currId: provider.currentCheckedId,
                                    ),
                                builder: (context, data, child) {
                                  print('重新构建 tabs');
                                  return AppTabs(
                                    key: _tabsKey,
                                    size: TabsSize.large,
                                    items: data.items,
                                    showBorderLine: false,
                                    selectedId: data.currId,
                                    onTabSelected: (tabId) => _handleTabSelected(context, tabId),
                                    onTabClosed: (tabId) => _handleTabClosed(context, tabId),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // 上部分：左右布局的按钮区 - 不参与水平滚动
                  Container(
                    height: titleHeight,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      border: Border(bottom: BorderSide(color: borderColor, width: 1.0)),
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 10),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween, // 左右两边对齐，中间留白
                      children: [
                        // 左侧按钮组 - 靠左显示
                        Expanded(
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Wrap(
                              spacing: 10,
                              children: [
                                AppButton(
                                  text: '添加',
                                  iconData: Icons.add,
                                  type: ButtonType.primary,

                                  // backgroundColor: AppColors.primary,
                                  onPressed: () {
                                    print('点击了添加按钮');
                                  },
                                ),
                                SizedBox(
                                  height: buttonHeight,
                                  child: VerticalDivider(width: 1, color: borderColor),
                                ),
                                AppButton(text: '全部', type: ButtonType.primary, onPressed: () {}),
                                AppButton(text: '我负责的', onPressed: () {}),
                                AppButton(text: '我参与的', onPressed: () {}),
                                AppButton(text: '我的自定义', onPressed: () {}),
                                AppButton(iconData: Icons.expand_more, onPressed: () {}),
                                SizedBox(
                                  height: buttonHeight,
                                  child: VerticalDivider(width: 1, color: borderColor),
                                ),
                                AppButton(
                                  text: '新增视图',
                                  iconData: Icons.add,
                                  backgroundColor: AppColors.white,
                                  type: ButtonType.transparent,
                                  onPressed: () {},
                                  // backgroundColor: AppColors.white,
                                ),
                              ],
                            ),
                          ),
                        ),
                        // 右侧按钮组 - 靠右显示
                        Wrap(
                          spacing: 10,
                          children: [
                            AppToggleButton(
                              value: viewModel,
                              btns: buttonList,
                              onChanged: (value) {
                                setState(() {
                                  viewModel = value;
                                });
                              },
                            ),
                            SizedBox(
                              height: buttonHeight,
                              child: VerticalDivider(width: 1, color: borderColor),
                            ),
                            AppButton(iconData: IconFont.xianxing_ziduanpeizhi, onPressed: () {}),
                            AppButton(iconData: IconFont.xianxing_sousuo, onPressed: () {}),
                            AppButton(iconData: IconFont.xianxing_shaixuan, onPressed: () {}),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // 主内容区域：根据视图模式显示不同的视图
                  Expanded(
                    child:
                        viewModel == 1
                            ? ProjectsTabDashboard(tabsKey: _tabsKey)
                            : ProjectsTabTable(tabsKey: _tabsKey),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}
