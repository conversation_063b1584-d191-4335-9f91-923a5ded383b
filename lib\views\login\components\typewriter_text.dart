import 'package:flutter/material.dart';
import 'dart:async';
import 'package:octasync_client/views/login/provider/login_provider.dart';
import 'package:provider/provider.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 打字机效果
class TypewriterText extends StatefulWidget {
  const TypewriterText({super.key});

  @override
  State<TypewriterText> createState() => _TypewriterTextState();
}

class _TypewriterTextState extends State<TypewriterText> {
  static const String _allText = '你好!';
  String _currentText = "";
  Timer? _typeTimer;
  Timer? _cursorTimer;
  bool _showCursor = true;

  /// 用户凭证信息
  UserCredential _userCredential = UserCredential();

  /// 打字机动画是否完成
  bool _typewriterCompleted = false;

  @override
  void initState() {
    super.initState();
    _startCursorEffect();
    _getSavedCredential();

    /// 延迟启动
    Timer(Duration(milliseconds: 1500), () {
      if (!mounted) return;
      _startTypewriterEffect();
    });
  }

  /// 打字机效果
  void _startTypewriterEffect() {
    int index = 0;
    _typeTimer = Timer.periodic(Duration(milliseconds: 400), (timer) {
      if (index >= _allText.length) {
        setState(() {
          _showCursor = false;
        });
        _typeTimer?.cancel();
        _cursorTimer?.cancel();
        _typewriterCompleted = true;
        return;
      }
      setState(() {
        _currentText = _allText.substring(0, index + 1);
        index++;
      });
    });
  }

  /// 光标闪烁
  void _startCursorEffect() {
    _cursorTimer = Timer.periodic(Duration(milliseconds: 500), (timer) {
      setState(() {
        _showCursor = !_showCursor;
      });
    });
  }

  /// 获取用户凭证
  void _getSavedCredential() async {
    final userCredential = await StorageUtil.getUserCredential();
    if (mounted) {
      setState(() {
        _userCredential = userCredential;
      });
    }
  }

  @override
  void dispose() {
    _typeTimer?.cancel();
    _cursorTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final inputAccount = context.select((LoginProvider model) => model.inputAccount);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(color: AppColors.primary.withValues(alpha: .1)),
      child: IntrinsicWidth(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _currentText,
              style: TextStyle(color: AppColors.primary, fontSize: 28, fontWeight: FontWeight.bold),
            ),
            // 显示光标效果
            Text(
              "|",
              style: TextStyle(
                color: _showCursor ? AppColors.primary : Colors.transparent,
                fontSize: 24,
                height: 1.5,
                fontWeight: FontWeight.bold,
              ),
            ),
            // 用户头像
            if (_typewriterCompleted &&
                inputAccount.trim() == _userCredential.account.trim() &&
                _userCredential.avatar != null)
              ImageCacheUtil.cachedAvatarImage(imageUrl: _userCredential.avatar!, size: 32),
          ],
        ),
      ),
    );
  }
}
