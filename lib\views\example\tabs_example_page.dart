import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// AppTabs组件使用示例
///
/// 本组件展示了AppTabs的各种用法和参数配置
class TabsExamplePage extends StatefulWidget {
  const TabsExamplePage({super.key});

  @override
  State<TabsExamplePage> createState() => _TabsExamplePageState();
}

class _TabsExamplePageState extends State<TabsExamplePage> {
  // 当前选中的标签页ID
  int _selectedId = 1;

  // 边框显示控制示例的选中ID
  int _borderSelectedId = 1;

  // 滚动控制示例的选中ID
  int _scrollSelectedId = 1;

  // 尺寸控制示例的选中ID
  int _sizeSelectedId = 1;

  // 类型控制示例的选中ID
  int _typeSelectedId = 1;

  // 类型控制示例的标签页选中ID
  int _typeTabSelectedId = 1;

  // 标签页列表
  final List<TabItem> _items = [
    TabItem(id: 1, title: '患者信息'),
    TabItem(id: 2, title: '检查报告'),
    TabItem(id: 3, title: '用药记录'),
    TabItem(id: 4, title: '手术记录'),
    TabItem(id: 5, title: '护理记录'),
    TabItem(id: 6, title: '医嘱信息'),
    TabItem(id: 7, title: '费用明细'),
    TabItem(id: 8, title: '出院小结'),
    TabItem(id: 9, title: '出院小结9'),
    TabItem(id: 10, title: '出院小结10'),
    TabItem(id: 11, title: '出院小结11'),
    TabItem(id: 12, title: '出院小结12'),
  ];

  // 边框显示控制示例的标签页列表
  final List<TabItem> _borderItems = [
    TabItem(id: 1, title: '标签页1'),
    TabItem(id: 2, title: '标签页2'),
    TabItem(id: 3, title: '标签页3'),
    TabItem(id: 4, title: '标签页4'),
  ];

  // 滚动控制示例的标签页列表
  final List<TabItem> _scrollItems = [
    TabItem(id: 1, title: '标签页1'),
    TabItem(id: 2, title: '标签页2'),
    TabItem(id: 3, title: '标签页3'),
    TabItem(id: 4, title: '标签页4'),
    TabItem(id: 5, title: '标签页5'),
    TabItem(id: 6, title: '标签页6'),
    TabItem(id: 7, title: '标签页7'),
    TabItem(id: 8, title: '标签页8'),
    TabItem(id: 9, title: '标签页9'),
    TabItem(id: 10, title: '标签页10'),
    TabItem(id: 11, title: '标签页11'),
    TabItem(id: 12, title: '标签页12'),
  ];

  // 尺寸控制示例的标签页列表
  final List<TabItem> _sizeItems = [
    TabItem(id: 1, title: '标签页1'),
    TabItem(id: 2, title: '标签页2'),
    TabItem(id: 3, title: '标签页3'),
    TabItem(id: 4, title: '标签页4'),
  ];

  // 类型控制示例的标签页列表
  final List<TabItem> _typeItems = [
    TabItem(id: 1, title: '标签页1'),
    TabItem(id: 2, title: '标签页2'),
    TabItem(id: 3, title: '标签页3'),
    TabItem(id: 4, title: '标签页4'),
  ];

  // 标签页控制器
  final GlobalKey<AppTabsState> _tabsKey = GlobalKey<AppTabsState>();
  final GlobalKey<AppTabsState> _borderTabsKey = GlobalKey<AppTabsState>();
  final GlobalKey<AppTabsState> _scrollTabsKey = GlobalKey<AppTabsState>();
  final GlobalKey<AppTabsState> _sizeTabsKey = GlobalKey<AppTabsState>();
  final GlobalKey<AppTabsState> _typeTabsKey = GlobalKey<AppTabsState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('AppTabs 组件演示')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('1. 基本用法'),
            _buildBasicExample(),

            const SizedBox(height: 32),
            _buildSectionTitle('2. 属性说明'),
            _buildPropertiesDescription(),

            const SizedBox(height: 32),
            _buildSectionTitle('3. 事件处理'),
            _buildEventHandlingExample(),

            const SizedBox(height: 32),
            _buildSectionTitle('4. 样式定制'),
            _buildStyleCustomizationExample(),

            const SizedBox(height: 32),
            _buildSectionTitle('5. 边框显示控制'),
            _buildBorderLineExample(),

            const SizedBox(height: 32),
            _buildSectionTitle('6. 滚动控制'),
            _buildScrollControlExample(),

            const SizedBox(height: 32),
            _buildSectionTitle('7. 尺寸控制'),
            _buildSizeControlExample(),

            const SizedBox(height: 32),
            _buildSectionTitle('8. 类型控制'),
            _buildTypeControlExample(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
    );
  }

  /// 基本用法示例
  Widget _buildBasicExample() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('基础标签页示例：', style: TextStyle(fontSize: 16)),
          const SizedBox(height: 16),
          AppTabs(
            key: _tabsKey,
            items: _items,
            selectedId: _selectedId,
            onTabSelected: (id) {
              setState(() {
                _selectedId = id;
              });
            },
            onTabClosed: (id) {
              setState(() {
                _items.removeWhere((item) => item.id == id);
                if (_selectedId == id && _items.isNotEmpty) {
                  _selectedId = _items.first.id;
                }
              });
            },
          ),
          const SizedBox(height: 16),
          Text('当前选中标签页ID: $_selectedId'),
        ],
      ),
    );
  }

  /// 属性说明
  Widget _buildPropertiesDescription() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: const [
          Text('AppTabs 组件属性说明：', style: TextStyle(fontSize: 16)),
          SizedBox(height: 16),
          Text('1. items: List<TabItem> - 标签页数据列表'),
          Text('2. selectedId: int - 当前选中的标签页ID'),
          Text('3. onTabSelected: Function(int) - 标签页选中回调'),
          Text('4. onTabClosed: Function(int) - 标签页关闭回调'),
          Text('5. showBorderLine: bool - 是否显示边框线，默认为true'),
          Text('6. size: TabsSize - 标签页尺寸，可选值为large(44px)和medium(40px)，默认为medium'),
          Text('7. type: TabsType - 标签页类型，可选值为card(显示边框)和text(不显示边框)，默认为card'),
          SizedBox(height: 8),
          Text('TabItem 模型属性：'),
          Text('1. id: int - 标签页唯一标识'),
          Text('2. title: String - 标签页标题'),
          SizedBox(height: 8),
          Text('AppTabsState 方法：'),
          Text('1. scrollToTab(int tabId) - 滚动到指定ID的标签页'),
        ],
      ),
    );
  }

  /// 事件处理示例
  Widget _buildEventHandlingExample() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('事件处理示例：', style: TextStyle(fontSize: 16)),
          const SizedBox(height: 16),
          Row(
            children: [
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    final newId =
                        _items.isEmpty
                            ? 1
                            : _items.map((e) => e.id).reduce((a, b) => a > b ? a : b) + 1;
                    _items.add(TabItem(id: newId, title: '新标签页 $newId'));
                    _selectedId = newId;
                  });
                },
                child: const Text('添加标签页'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {
                  if (_items.isNotEmpty) {
                    setState(() {
                      _items.removeLast();
                      if (_selectedId == _items.last.id) {
                        _selectedId = _items.first.id;
                      }
                    });
                  }
                },
                child: const Text('删除最后一个标签页'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          AppTabs(
            items: _items,
            selectedId: _selectedId,
            onTabSelected: (id) {
              setState(() {
                _selectedId = id;
              });
            },
            onTabClosed: (id) {
              setState(() {
                _items.removeWhere((item) => item.id == id);
                if (_selectedId == id && _items.isNotEmpty) {
                  _selectedId = _items.first.id;
                }
              });
            },
          ),
        ],
      ),
    );
  }

  /// 样式定制示例
  Widget _buildStyleCustomizationExample() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('样式定制说明：', style: TextStyle(fontSize: 16)),
          const SizedBox(height: 16),
          const Text('AppTabs 组件支持以下样式定制：'),
          const SizedBox(height: 8),
          const Text('1. 标签页宽度：通过 _itemWidth 属性控制'),
          const Text('2. 标签页间距：通过 _itemPadding 属性控制'),
          const Text('3. 标签页圆角：通过 _radiusStyle 属性控制'),
          const Text('4. 选中状态颜色：通过 context.background300 控制'),
          const Text('5. 文字颜色：通过 context.textPrimary 控制'),
          const Text('6. 图标颜色：通过 context.icon300 控制'),
          const SizedBox(height: 16),
          const Text('注意：样式定制需要在组件源码中进行修改，不支持运行时配置。'),
        ],
      ),
    );
  }

  /// 边框显示控制示例
  Widget _buildBorderLineExample() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('边框显示控制示例：', style: TextStyle(fontSize: 16)),
          const SizedBox(height: 16),
          Row(
            children: [
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _borderSelectedId = 1;
                  });
                },
                child: const Text('显示边框'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _borderSelectedId = 2;
                  });
                },
                child: const Text('隐藏边框'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          AppTabs(
            key: _borderTabsKey,
            items: _borderItems,
            selectedId: _borderSelectedId,
            showBorderLine: _borderSelectedId == 1, // 根据选中状态控制边框显示
            onTabSelected: (id) {
              setState(() {
                _borderSelectedId = id;
              });
            },
            onTabClosed: (id) {
              setState(() {
                _borderItems.removeWhere((item) => item.id == id);
                if (_borderSelectedId == id && _borderItems.isNotEmpty) {
                  _borderSelectedId = _borderItems.first.id;
                }
              });
            },
          ),
          const SizedBox(height: 16),
          Text('当前边框状态: ${_borderSelectedId == 1 ? "显示" : "隐藏"}'),
        ],
      ),
    );
  }

  /// 滚动控制示例
  Widget _buildScrollControlExample() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('滚动控制示例：', style: TextStyle(fontSize: 16)),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              for (var i = 1; i <= 12; i++)
                ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _scrollSelectedId = i;
                    });
                    // 使用scrollToTab方法滚动到指定标签页
                    _scrollTabsKey.currentState?.scrollToTab(i);
                  },
                  child: Text('滚动到标签 $i'),
                ),
            ],
          ),
          const SizedBox(height: 16),
          AppTabs(
            key: _scrollTabsKey,
            items: _scrollItems,
            selectedId: _scrollSelectedId,
            onTabSelected: (id) {
              setState(() {
                _scrollSelectedId = id;
              });
            },
            onTabClosed: (id) {
              setState(() {
                _scrollItems.removeWhere((item) => item.id == id);
                if (_scrollSelectedId == id && _scrollItems.isNotEmpty) {
                  _scrollSelectedId = _scrollItems.first.id;
                }
              });
            },
          ),
          const SizedBox(height: 16),
          Text('当前选中标签页ID: $_scrollSelectedId'),
          const SizedBox(height: 8),
          const Text('说明：点击上方按钮可以滚动到对应的标签页位置。'),
        ],
      ),
    );
  }

  /// 尺寸控制示例
  Widget _buildSizeControlExample() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('尺寸控制示例：', style: TextStyle(fontSize: 16)),
          const SizedBox(height: 16),
          Row(
            children: [
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _sizeSelectedId = 1;
                  });
                },
                child: const Text('大型标签页'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _sizeSelectedId = 2;
                  });
                },
                child: const Text('中型标签页'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          AppTabs(
            key: _sizeTabsKey,
            items: _sizeItems,
            selectedId: _sizeSelectedId,
            size: _sizeSelectedId == 1 ? TabsSize.large : TabsSize.medium,
            onTabSelected: (id) {
              setState(() {
                _sizeSelectedId = id;
              });
            },
            onTabClosed: (id) {
              setState(() {
                _sizeItems.removeWhere((item) => item.id == id);
                if (_sizeSelectedId == id && _sizeItems.isNotEmpty) {
                  _sizeSelectedId = _sizeItems.first.id;
                }
              });
            },
          ),
          const SizedBox(height: 16),
          Text('当前尺寸: ${_sizeSelectedId == 1 ? "大型(44px)" : "中型(40px)"}'),
          const SizedBox(height: 8),
          const Text('说明：点击上方按钮可以切换标签页的尺寸。'),
        ],
      ),
    );
  }

  /// 类型控制示例
  Widget _buildTypeControlExample() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('类型控制示例：', style: TextStyle(fontSize: 16)),
          const SizedBox(height: 16),
          Row(
            children: [
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _typeSelectedId = 1;
                  });
                },
                child: const Text('卡片样式'),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _typeSelectedId = 2;
                  });
                },
                child: const Text('文本样式'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          AppTabs(
            key: _typeTabsKey,
            items: _typeItems,
            selectedId: _typeTabSelectedId,
            type: _typeSelectedId == 1 ? TabsType.card : TabsType.text,
            onTabSelected: (id) {
              setState(() {
                _typeTabSelectedId = id;
              });
            },
            onTabClosed: (id) {
              setState(() {
                _typeItems.removeWhere((item) => item.id == id);
                if (_typeTabSelectedId == id && _typeItems.isNotEmpty) {
                  _typeTabSelectedId = _typeItems.first.id;
                }
              });
            },
          ),
          const SizedBox(height: 16),
          Text('当前类型: ${_typeSelectedId == 1 ? "卡片样式" : "文本样式"}'),
          const SizedBox(height: 8),
          const Text('说明：点击上方按钮可以切换标签页的显示类型。'),
        ],
      ),
    );
  }
}
