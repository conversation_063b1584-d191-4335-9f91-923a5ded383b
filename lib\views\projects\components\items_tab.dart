import 'package:flutter/material.dart';
import 'package:octasync_client/views/projects/components/projects_tab/tab_title.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class ItemsTab extends StatefulWidget {
  const ItemsTab({super.key});

  @override
  State<ItemsTab> createState() => _ItemsTabState();
}

class _ItemsTabState extends State<ItemsTab> {
  final double titleHeight = Variables.titleHeight;

  @override
  void dispose() {
    // 确保在Widget销毁时移除弹出组件
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var borderColor = Variables.commonBorderColor(context);
    return Scaffold(
      body: Column(
        children: [
          Container(
            height: titleHeight,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: borderColor, // 边框颜色
                  width: 1.0, // 边框宽度
                ),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.only(left: 5, right: 5),
              child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [TabTitle()]),
            ),
          ),
          Expanded(child: Center()),
        ],
      ),
    );
  }
}
