import 'package:flutter/material.dart';
import 'package:octasync_client/views/projects/providers/sidebar_provider.dart';
import 'package:provider/provider.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class SideBar extends StatefulWidget {
  SideBar({super.key});

  @override
  State<SideBar> createState() => _SideBarState();
}

class _SideBarState extends State<SideBar> {
  final double _pd = 10;

  @override
  void initState() {
    super.initState();

    // // 使用 addPostFrameCallback 确保在第一帧渲染完成后调用
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   // 此时可以安全地访问 Provider
    //   getSideBarSummary();
    // });

    // 使用 Future.microtask 在当前事件循环结束后执行
    Future.microtask(() {
      //模拟修改数据
      getSideBarSummary();
      getSideBarSummary2();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 10, bottom: 10),
      // child:
      child: Selector<SideBarProvider, SideBarProvider>(
        shouldRebuild: (pre, next) => false,
        selector: (context, provider) => provider,
        builder: (context, provider, child) {
          return Column(children: _buildSideBarItemList(context, provider));
        },
      ),
    );
  }

  /// 生成items列表
  List<Widget> _buildSideBarItemList(BuildContext context, SideBarProvider provider) {
    var borderColor = Variables.commonBorderColor(context);
    List<Widget> widgetList = []; // 用于存储生成的widget

    for (var i = 0; i < provider.items.length; i++) {
      widgetList.add(
        Selector<SideBarProvider, ({SideBarItem item, int checkId})>(
          selector:
              (context, provider) => (item: provider.items[i], checkId: provider.currentCheckedId),
          builder: (context, data, child) {
            return Column(
              children: [
                // Container(
                //   margin: EdgeInsets.only(left: _pd, right: _pd),
                //   child: Material(
                //     color:
                //         data.checkId != data.item.id ? Colors.transparent : context.background300,
                //     borderRadius: BorderRadius.circular(40),
                //     child: InkWell(
                //       onTap: () => provider.changeCheckedId(data.item.id),
                //       child: _buildSideBarItem(data.item),
                //     ),
                //   ),
                // ),
                Container(
                  margin: EdgeInsets.only(left: _pd, right: _pd),
                  child: Material(
                    color:
                        data.checkId != data.item.id ? Colors.transparent : context.background300,
                    borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(AppRadiusSize.radius4),
                      onTap: () => provider.changeCheckedId(data.item.id),
                      child: _buildSideBarItem(data.item),
                    ),
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 4, bottom: 4, left: _pd, right: _pd),
                  child: Padding(
                    padding: EdgeInsets.only(top: 0, bottom: 0),
                    child: Divider(color: borderColor),
                  ),
                ),
              ],
            );
          },
        ),
      );
    }

    return widgetList;
  }

  // 生成侧边栏单个item
  Widget _buildSideBarItem(SideBarItem item) {
    return Padding(
      padding: EdgeInsets.only(left: _pd, right: _pd),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center, // 垂直居中
        children: [
          SizedBox(
            height: 40,
            child: Padding(
              padding: EdgeInsets.only(top: 2),
              child: Icon(item.icon, size: AppIconSize.small, color: item.color),
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(
                left: Variables.iconTextSpace,
                right: Variables.iconTextSpace,
              ),
              child: Text(item.title),
            ),
          ),
          Text(item.total > 99 ? '99+' : item.total.toString()),
        ],
      ),
    );
  }

  Future<void> getSideBarSummary() async {
    /// 请求api接口，更新统计数据
    try {
      // 模拟网络延迟
      await Future.delayed(Duration(seconds: 2));

      // 模拟API返回的数据
      final mockResponse = [
        {'id': 1, 'total': 15},
        {'id': 2, 'total': 20},
        {'id': 3, 'total': 25},
        {'id': 4, 'total': 300},
      ];

      if (!mounted) return;

      final myProvider = context.read<SideBarProvider>();
      // print('修改total');
      // Provider.of<SideBarProvider>(context, listen: false).changeTotal(id: 1, total: 11);
      // myProvider.changeTotal(id: 1, total: 11);

      for (var data in mockResponse) {
        myProvider.changeTotal(id: data['id'] as int, total: data['total'] as int);
      }
    } catch (e) {
      print('Error in mock network data: $e');
    }
  }

  Future<void> getSideBarSummary2() async {
    /// 请求api接口，更新统计数据
    try {
      // 模拟网络延迟
      await Future.delayed(Duration(seconds: 5));

      // 模拟API返回的数据
      final mockResponse = [
        {'id': 1, 'total': 15},
        {'id': 2, 'total': 20},
        {'id': 3, 'total': 25},
        {'id': 4, 'total': 300},
      ];

      if (!mounted) return;

      final myProvider = context.read<SideBarProvider>();
      // print('修改total');
      // Provider.of<SideBarProvider>(context, listen: false).changeTotal(id: 1, total: 11);

      myProvider.changeTotal(id: 2, total: 83);

      // for (var data in mockResponse) {
      //   print('object');
      //   myProvider.changeTotal(id: data['id'] as int, total: data['total'] as int);
      // }
    } catch (e) {
      print('Error in mock network data: $e');
    }
  }
}
