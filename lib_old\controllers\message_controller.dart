import 'package:get/get.dart';
import '../models/message.dart';

class MessageController extends GetxController {
  final messages = <Message>[].obs;
  final unreadMessages = <Message>[].obs;

  @override
  void onInit() {
    super.onInit();
    // 模拟一些测试数据
    messages.addAll([
      Message(
        id: '1',
        senderId: 'user1',
        senderName: '张三',
        content: '你好!',
        timestamp: DateTime.now().subtract(const Duration(minutes: 5)),
      ),
      Message(
        id: '2',
        senderId: 'user2',
        senderName: '李四',
        content: '在吗？',
        timestamp: DateTime.now().subtract(const Duration(minutes: 2)),
      ),
    ]);
    
    _updateUnreadMessages();
  }

  void _updateUnreadMessages() {
    unreadMessages.value = messages.where((msg) => !msg.isRead).toList();
  }

  void markAsRead(String messageId) {
    final index = messages.indexWhere((msg) => msg.id == messageId);
    if (index != -1) {
      final message = messages[index];
      messages[index] = Message(
        id: message.id,
        senderId: message.senderId,
        senderName: message.senderName,
        content: message.content,
        timestamp: message.timestamp,
        isRead: true,
      );
      _updateUnreadMessages();
    }
  }
}
