import 'package:flutter/cupertino.dart';

import 'package:json_annotation/json_annotation.dart';

part 'general_option.g.dart';

/// 通用键值对
@JsonSerializable(genericArgumentFactories: true)
class GeneralOption<T> {
  T id;
  String text;
  @JsonKey(ignore: true)
  Icon? icon;
  String? symbol;
  GeneralOption({required this.id, required this.text, this.icon, this.symbol});

  factory GeneralOption.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) => _$GeneralOptionFromJson(json, fromJsonT);

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$GeneralOptionToJson(this, toJsonT);
}

class GeneralOptionGroup<T> {
  final String text; // 组名
  final List<GeneralOption<T>> items;
  GeneralOptionGroup({required this.text, required this.items});
}
