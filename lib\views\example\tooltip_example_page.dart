import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// AppCustomTooltip组件使用示例
///
/// 本组件展示了AppCustomTooltip的各种用法和参数配置
class TooltipExamplePage extends StatefulWidget {
  const TooltipExamplePage({super.key});

  @override
  State<TooltipExamplePage> createState() => _TooltipExamplePageState();
}

class _TooltipExamplePageState extends State<TooltipExamplePage> {
  // 控制手动模式下提示框的显示状态
  AppCustomTooltipController controller1 = AppCustomTooltipController();
  bool isShowTooltip1 = false;
  AppCustomTooltipController controller2 = AppCustomTooltipController();
  bool isShowTooltip2 = false;
  AppCustomTooltipController controller3 = AppCustomTooltipController();
  bool isShowTooltip3 = false;

  // 控制点击外部关闭示例的提示框显示状态
  bool isShowOutsideClickTooltip1 = false;
  AppCustomTooltipController controller4 = AppCustomTooltipController();
  bool isShowOutsideClickTooltip2 = false;
  AppCustomTooltipController controller5 = AppCustomTooltipController();

  // 定义不同位置的提示框示例
  List<TooltipPlacement> row1 = [
    TooltipPlacement.topLeft,
    TooltipPlacement.topCenter,
    TooltipPlacement.topRight,
  ];

  List<TooltipPlacement> col1 = [
    TooltipPlacement.leftTop,
    TooltipPlacement.leftCenter,
    TooltipPlacement.leftBottom,
  ];

  List<TooltipPlacement> col2 = [
    TooltipPlacement.rightTop,
    TooltipPlacement.rightCenter,
    TooltipPlacement.rightBottom,
  ];

  List<TooltipPlacement> row2 = [
    TooltipPlacement.bottomLeft,
    TooltipPlacement.bottomCenter,
    TooltipPlacement.bottomRight,
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('AppCustomTooltip 组件演示')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('1. 提示框位置演示 (TooltipPlacement)'),
            _buildPositionDemos(),

            const SizedBox(height: 32),
            _buildSectionTitle('2. 手动控制模式 (manual: true)'),
            _buildManualControlDemos(),

            const SizedBox(height: 32),
            _buildSectionTitle('3. 动画效果演示 (tooltipAnimationType)'),
            _buildAnimationDemos(),

            const SizedBox(height: 32),
            _buildSectionTitle('4. 等待显示时间 (waitDuration)'),
            _buildWaitDurationDemos(),

            const SizedBox(height: 32),
            _buildSectionTitle('5. 自定义样式演示'),
            _buildCustomStyleDemos(),

            const SizedBox(height: 32),
            _buildSectionTitle('6. 点击外部区域关闭提示框 (closeOnOutsideClick)'),
            _buildOutsideClickDemos(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
    );
  }

  /// 提示框位置演示
  Widget _buildPositionDemos() {
    var borderColor = Colors.grey[300]!;

    return Container(
      width: 800,
      child: Column(
        children: [
          // 顶部位置演示
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Wrap(
                spacing: 150,
                children: [
                  for (var placement in row1)
                    AppCustomTooltip(
                      placement: placement,
                      content: placement.name,
                      child: Container(
                        padding: EdgeInsets.all(5),
                        decoration: BoxDecoration(border: Border.all(color: borderColor, width: 1)),
                        child: Text(placement.name),
                      ),
                    ),
                ],
              ),
            ],
          ),

          // 左右两侧位置演示
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // 左侧位置
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  for (var i = 0; i < col1.length; i++) ...[
                    AppCustomTooltip(
                      placement: col1[i],
                      content: col1[i].name,
                      child: Container(
                        padding: EdgeInsets.all(5),
                        decoration: BoxDecoration(border: Border.all(color: borderColor, width: 1)),
                        child: Text(col1[i].name),
                      ),
                    ),
                    if (i < col1.length - 1) SizedBox(height: 50),
                  ],
                ],
              ),

              // 右侧位置
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  for (var i = 0; i < col2.length; i++) ...[
                    AppCustomTooltip(
                      placement: col2[i],
                      content: col2[i].name,
                      child: Container(
                        padding: EdgeInsets.all(5),
                        decoration: BoxDecoration(border: Border.all(color: borderColor, width: 1)),
                        child: Text(col2[i].name),
                      ),
                    ),
                    if (i < col2.length - 1) SizedBox(height: 50),
                  ],
                ],
              ),
            ],
          ),

          // 底部位置演示
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Wrap(
                spacing: 150,
                children: [
                  for (var placement in row2)
                    AppCustomTooltip(
                      placement: placement,
                      content: placement.name,
                      child: Container(
                        padding: EdgeInsets.all(5),
                        decoration: BoxDecoration(border: Border.all(color: borderColor, width: 1)),
                        child: Text(placement.name),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 手动控制模式演示
  Widget _buildManualControlDemos() {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: [
        // 基本手动控制
        AppCustomTooltip(
          controller: controller1,
          manual: true,
          // isShow: isShowTooltip1,
          content: '手动控制的提示框',
          child: ElevatedButton(
            onPressed: () {
              controller1.toggleTooltip();
              setState(() {
                isShowTooltip1 = controller1.isShowTooltip;
              });
            },
            child: Text('切换提示框显示 (${isShowTooltip1 ? "显示中" : "隐藏中"})'),
          ),
        ),

        // 浅色主题手动控制
        AppCustomTooltip(
          manual: true,
          controller: controller2,
          // isShow: isShowTooltip2,
          content: '浅色主题提示框',
          effect: 'light',
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.amber),
            onPressed: () {
              controller2.toggleTooltip();
              setState(() {
                isShowTooltip2 = controller2.isShowTooltip;
              });
            },
            child: Text('浅色主题 (${isShowTooltip2 ? "显示中" : "隐藏中"})'),
          ),
        ),

        // 自定义内容手动控制
        AppCustomTooltip(
          manual: true,
          controller: controller3,
          // isShow: isShowTooltip3,
          content: '自定义内容提示框',
          effect: 'light',
          arrowColor: Colors.blue[100],
          contentChild: Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.blue[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue[800], size: 20),
                    SizedBox(width: 5),
                    Text('自定义提示内容', style: TextStyle(color: Colors.blue[800])),
                  ],
                ),
                SizedBox(height: 5),
                Text('可以包含任意Flutter组件', style: TextStyle(fontSize: 12, color: Colors.blue[600])),
              ],
            ),
          ),
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(backgroundColor: Colors.blue),
            onPressed: () {
              controller3.toggleTooltip();
              setState(() {
                isShowTooltip3 = controller3.isShowTooltip;
              });
            },
            child: Text('自定义内容 (${isShowTooltip3 ? "显示中" : "隐藏中"})'),
          ),
        ),
      ],
    );
  }

  /// 动画效果演示
  Widget _buildAnimationDemos() {
    var borderColor = Colors.grey[300]!;

    return Wrap(
      spacing: 32,
      runSpacing: 16,
      children: [
        Column(
          children: [
            AppCustomTooltip(
              placement: TooltipPlacement.topCenter,
              content: '淡入动画效果 (fadeIn)',
              tooltipAnimationType: TooltipAnimationType.fadeIn,
              child: Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  border: Border.all(color: borderColor, width: 1),
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.blue[50],
                ),
                child: Text('淡入动画效果 (fadeIn)'),
              ),
            ),
            SizedBox(height: 8),
            Text('默认效果，悬停查看', style: TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),

        Column(
          children: [
            AppCustomTooltip(
              placement: TooltipPlacement.topCenter,
              content: '缩放动画效果 (scaleIn)',
              tooltipAnimationType: TooltipAnimationType.scaleIn,
              child: Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  border: Border.all(color: borderColor, width: 1),
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.green[50],
                ),
                child: Text('缩放动画效果 (scaleIn)'),
              ),
            ),
            SizedBox(height: 8),
            Text('从小到大效果，悬停查看', style: TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),
      ],
    );
  }

  /// 等待时间演示
  Widget _buildWaitDurationDemos() {
    return Wrap(
      spacing: 32,
      runSpacing: 16,
      children: [
        Column(
          children: [
            AppCustomTooltip(
              content: '立即显示 (waitDuration: 0)',
              waitDuration: 0,
              child: Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.purple[100],
                ),
                child: Text('立即显示'),
              ),
            ),
            SizedBox(height: 8),
            Text('waitDuration: 0', style: TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),

        Column(
          children: [
            AppCustomTooltip(
              content: '默认等待时间 (waitDuration: 150)',
              waitDuration: 150,
              child: Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.indigo[100],
                ),
                child: Text('默认等待时间'),
              ),
            ),
            SizedBox(height: 8),
            Text('waitDuration: 150ms', style: TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),

        Column(
          children: [
            AppCustomTooltip(
              content: '较长等待时间 (waitDuration: 1000)',
              waitDuration: 1000,
              child: Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Colors.pink[100],
                ),
                child: Text('较长等待时间'),
              ),
            ),
            SizedBox(height: 8),
            Text('waitDuration: 1000ms', style: TextStyle(fontSize: 12, color: Colors.grey)),
          ],
        ),
      ],
    );
  }

  /// 自定义样式演示
  Widget _buildCustomStyleDemos() {
    return Wrap(
      spacing: 32,
      runSpacing: 24,
      children: [
        // 自定义颜色和边框
        AppCustomTooltip(
          placement: TooltipPlacement.topCenter,
          content: '自定义颜色和边框',
          effect: 'light',
          arrowColor: Colors.orangeAccent,
          contentChild: Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.orangeAccent,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 5,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              '自定义颜色和边框',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
          child: Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.orange[100],
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.orange),
            ),
            child: Text('自定义颜色和边框'),
          ),
        ),

        // 无箭头提示框
        AppCustomTooltip(
          placement: TooltipPlacement.topCenter,
          content: '无箭头提示框',
          showArrow: false,
          child: Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.teal[100],
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.teal),
            ),
            child: Text('无箭头提示框'),
          ),
        ),

        // 自定义动画时长
        AppCustomTooltip(
          placement: TooltipPlacement.topCenter,
          content: '自定义动画时长 (animationDuration: 1000)',
          animationDuration: 1000,
          tooltipAnimationType: TooltipAnimationType.scaleIn,
          child: Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.deepPurple[100],
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.deepPurple),
            ),
            child: Text('自定义动画时长'),
          ),
        ),

        // 自定义偏移量
        AppCustomTooltip(
          placement: TooltipPlacement.topCenter,
          content: '自定义偏移量 (offset: 30)',
          offset: 30,
          child: Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.red[100],
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.red),
            ),
            child: Text('自定义偏移量'),
          ),
        ),
      ],
    );
  }

  /// 点击外部区域关闭提示框演示
  Widget _buildOutsideClickDemos() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: Text(
            '在手动模式下(manual: true)，设置closeOnOutsideClick为true可以启用点击外部区域关闭功能。'
            '当用户点击提示框和触发组件以外的区域时，提示框会自动关闭，并通过onVisibleChange回调通知父组件。',
            style: TextStyle(fontSize: 14, color: Colors.grey[700]),
          ),
        ),
        Wrap(
          spacing: 32,
          runSpacing: 24,
          children: [
            Column(
              children: [
                AppCustomTooltip(
                  manual: true,
                  controller: controller4,
                  // isShow: isShowOutsideClickTooltip1,
                  closeOnOutsideClick: true,
                  onVisibleChange: (visible) {
                    setState(() {
                      isShowOutsideClickTooltip1 = visible;
                    });
                  },
                  content: '点击外部区域会自动关闭此提示框',
                  placement: TooltipPlacement.topCenter,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.cyan),
                    onPressed: () {
                      controller4.toggleTooltip();
                      setState(() {
                        isShowOutsideClickTooltip1 = controller4.isShowTooltip;
                      });
                    },
                    child: Text('点击显示/隐藏 (${isShowOutsideClickTooltip1 ? "显示中" : "隐藏中"})'),
                  ),
                ),
                SizedBox(height: 8),
                Text('点击提示框或按钮以外的区域可以关闭提示框', style: TextStyle(fontSize: 12, color: Colors.grey)),
              ],
            ),

            Column(
              children: [
                AppCustomTooltip(
                  manual: true,
                  controller: controller5,
                  // isShow: isShowOutsideClickTooltip2,
                  effect: 'light',
                  closeOnOutsideClick: true,
                  onVisibleChange: (visible) {
                    setState(() {
                      isShowOutsideClickTooltip2 = visible;
                    });
                  },
                  tooltipAnimationType: TooltipAnimationType.scaleIn,
                  content: '浅色主题 + 缩放动画 + 点击外部区域关闭',
                  placement: TooltipPlacement.bottomCenter,
                  showArrow: false,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.blueGrey),
                    onPressed: () {
                      controller5.toggleTooltip();
                      setState(() {
                        isShowOutsideClickTooltip2 = controller5.isShowTooltip;
                      });
                    },
                    child: Text('综合示例', style: TextStyle(color: Colors.white)),
                  ),
                ),
                SizedBox(height: 8),
                Text('组合多种效果 + 点击外部区域关闭', style: TextStyle(fontSize: 12, color: Colors.grey)),
              ],
            ),

            AppCustomTooltip(
              content: 'placement.name',
              child: AppButton(
                iconData: Icons.star_rounded,
                size: ButtonSize.small,
                color: AppColors.yellow,
                type: ButtonType.transparent,
                onPressed: () {},
              ),
            ),
          ],
        ),
      ],
    );
  }
}
