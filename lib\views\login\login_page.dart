import 'package:flutter/material.dart';
import 'package:octasync_client/api/enterprise_config.dart';
import 'package:octasync_client/views/login/components/typewriter_text.dart';
import 'package:octasync_client/views/login/components/banner_slider.dart';
import 'package:octasync_client/views/login/components/login_form.dart';
import 'package:octasync_client/views/login/provider/login_provider.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/providers/app_provider.dart';
import 'package:octasync_client/imports.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 登录页
///
/// 顶部内边距桌面端不显示
class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  // 登录页信息
  EnterpriseDetail _loginPageInfo = EnterpriseDetail();

  @override
  void initState() {
    super.initState();

    // 从缓存中获取登录页信息
    StorageUtil.getLoginPageInfo()
        .then((value) {
          setState(() {
            _loginPageInfo = value;
          });
        })
        .whenComplete(() {
          // 获取登录页信息
          _getLoginPageInfo();
        });

    // 第一帧渲染完成后执行
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        // 隐藏窗口栏
        Provider.of<AppProvider>(context, listen: false).setWindowBarContentVisible(false);
      }
    });
  }

  /// 获取登录页企业信息
  _getLoginPageInfo() async {
    final res = await EnterpriseConfigApi.getLoginPageInfo({});
    EnterpriseDetail reqLoginPageInfo = EnterpriseDetail.fromJson(res);

    /// 如果登录页企业信息与缓存中的登录页企业信息不一致，则更新登录页企业信息
    if (_loginPageInfo != reqLoginPageInfo) {
      setState(() {
        _loginPageInfo = EnterpriseDetail.fromJson(res);
      });
      await StorageUtil.setLoginPageInfo(_loginPageInfo);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => LoginProvider(),
      child: Scaffold(
        body: Stack(
          children: [
            Padding(
              padding: EdgeInsets.only(
                top: AppUtil.isDesktop ? 0 : 30,
                bottom: 30,
                left: 24,
                right: 24,
              ),
              child: Row(
                children: [
                  Expanded(flex: 5, child: BannerSlider()),
                  Expanded(
                    flex: 5,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 150),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        spacing: 8,
                        children: [
                          TypewriterText(),
                          const Text('欢迎使用', style: TextStyle(fontSize: 18)),
                          ShaderMask(
                            shaderCallback: (bounds) {
                              return LinearGradient(
                                colors: [const Color(0xFF1887FF), const Color(0xFF5DF799)],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ).createShader(bounds);
                            },
                            child: Text(
                              _loginPageInfo.enterpriseName,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 28,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          LoginForm(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              bottom: 10,
              right: 10,
              child: ImageCacheUtil.cachedNetworkImage(
                imageUrl: _loginPageInfo.enterpriseClientImage,
                width: 64,
                height: 64,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
