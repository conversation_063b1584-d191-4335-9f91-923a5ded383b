import 'package:flutter/material.dart';
import 'package:octasync_client/layout/window_bar/window_avatar.dart';
import 'package:octasync_client/layout/window_bar/window_bar.dart';
import 'package:octasync_client/providers/router/base_router_provider.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/models/route_item.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 主应用外壳组件
///
/// 用于显示应用程序的导航菜单和页面内容。
/// 作为ShellRoute的包装器，它包含:
/// 1. 顶部的WindowBar (窗口标题栏)
/// 2. 左侧的垂直菜单列表
/// 3. 主内容区域
///
/// 此组件仅用于显示菜单的路由，不显示菜单的路由使用WindowBarWrapper。
class DesktopMainShell extends StatelessWidget {
  const DesktopMainShell({required this.child, super.key});

  final Widget child;

  void hanleMenuItem(BuildContext context, RouteItem route) {
    context.go(route.path);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          if (AppUtil.isDesktop) const WindowBar(),
          Expanded(
            child: Row(
              children: [
                Container(
                  color: context.windowBarColor,
                  child: SizedBox(
                    width: 64,
                    child: Padding(
                      padding: const EdgeInsets.all(5),
                      child: Column(
                        spacing: 5,
                        children: [
                          if (AppUtil.isMacOS) const WindowAvatar(),
                          ..._buildMenuItems(context),
                        ],
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    width: double.infinity,
                    height: double.infinity,
                    color: context.windowBarColor,
                    padding: const EdgeInsets.only(right: 8),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(AppRadiusSize.radius6),
                        topRight: Radius.circular(AppRadiusSize.radius6),
                      ),
                      child: child,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建并排序菜单项
  List<Widget> _buildMenuItems(BuildContext context) {
    final routerProvider = context.watch<BaseRouterProvider>();

    // 获取菜单路由并根据order排序
    final routes = routerProvider.getMenuRoutes.toList();
    routes.sort((a, b) => (a.order ?? 99).compareTo(b.order ?? 99));

    // 构建菜单项列表
    return routes.map((route) => _buildMenuItem(context, route)).toList();
  }

  Widget _buildMenuItem(BuildContext context, RouteItem route) {
    bool isActive() {
      final String location = GoRouterState.of(context).uri.path;
      return location.startsWith(route.path);
    }

    final curMenuColor = isActive() ? AppColors.primary : context.icon300;

    return SizedBox(
      width: 54,
      height: 54,
      child: Material(
        color: isActive() ? context.activeWhiteColor : Colors.transparent,
        borderRadius: BorderRadius.circular(4),
        child: InkWell(
          onTap: () => hanleMenuItem(context, route),
          borderRadius: BorderRadius.circular(4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(route.icon!, size: AppIconSize.small, color: curMenuColor),
              Text(route.title!, style: TextStyle(fontSize: 10, color: curMenuColor)),
            ],
          ),
        ),
      ),
    );
  }
}
