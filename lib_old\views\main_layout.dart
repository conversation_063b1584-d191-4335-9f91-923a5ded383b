import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/nav_controller.dart';
import '../controllers/tab_controller.dart' as app_tab;

class MainLayout extends StatelessWidget {
  MainLayout({super.key});

  final NavController navController = Get.put(NavController());
  final app_tab.TabsController tabsController =
      Get.put(app_tab.TabsController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // 左侧导航栏
          Obx(() => NavigationRail(
                selectedIndex: navController.currentIndex,
                onDestinationSelected: (index) {
                  navController.changePage(index);
                  // 当点击导航项时，添加一个新标签页
                  _addNewTab(index);
                },
                labelType: NavigationRailLabelType.all,
                destinations: const [
                  NavigationRailDestination(
                    icon: Icon(Icons.message),
                    label: Text('消息'),
                  ),
                  NavigationRailDestination(
                    icon: Icon(Icons.people),
                    label: Text('联系人'),
                  ),
                  NavigationRailDestination(
                    icon: Icon(Icons.settings),
                    label: Text('设置'),
                  ),
                ],
              )),
          // 右侧内容区
          Expanded(
            child: Column(
              children: [
                // 自定义标签栏
                _buildCustomTabBar(),
                // 内容区
                Expanded(
                  child: Obx(() {
                    if (tabsController.tabs.isEmpty) {
                      return const Center(
                        child: Text('请从左侧菜单选择功能'),
                      );
                    }

                    return IndexedStack(
                      index: tabsController.currentIndex.value,
                      children: tabsController.tabContents,
                    );
                  }),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建自定义标签栏
  Widget _buildCustomTabBar() {
    return Obx(() {
      if (tabsController.tabs.isEmpty) {
        return const SizedBox(height: 46); // 保持一致的高度
      }

      return Container(
        height: 46,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.shade300,
              width: 1,
            ),
          ),
        ),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: tabsController.tabs.length,
          itemBuilder: (context, index) {
            // 使用 Obx 包装每个标签项，确保它能响应 currentIndex 的变化
            return Obx(() {
              final tab = tabsController.tabs[index];
              final isSelected = index == tabsController.currentIndex.value;

              return InkWell(
                onTap: () => tabsController.changeTab(index),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color:
                        isSelected ? Colors.blue.shade50 : Colors.transparent,
                    border: Border(
                      bottom: BorderSide(
                        color: isSelected ? Colors.blue : Colors.transparent,
                        width: 2,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        tab,
                        style: TextStyle(
                          color: isSelected ? Colors.blue : Colors.black87,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      const SizedBox(width: 8),
                      InkWell(
                        onTap: () => tabsController.closeTab(index),
                        child: Icon(
                          Icons.close,
                          size: 16,
                          color: isSelected ? Colors.blue : Colors.grey,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            });
          },
        ),
      );
    });
  }

  // 添加新标签页
  void _addNewTab(int navIndex) {
    String tabTitle;
    Widget content;

    switch (navIndex) {
      case 0:
        tabTitle = '消息';
        content = DefaultTabController(
          length: navController.tabs.length,
          child: Column(
            children: [
              TabBar(
                tabs: navController.tabs,
                labelColor: Colors.black,
                unselectedLabelColor: Colors.grey,
                onTap: (index) {
                  // 只需更新 NavController 中的索引
                  navController.changeTab(index);
                },
              ),
              Expanded(
                child: TabBarView(
                  children: navController.tabViews,
                ),
              ),
            ],
          ),
        );
        break;
      case 1:
        tabTitle = '联系人';
        content = DefaultTabController(
          length: navController.tabs.length,
          child: Column(
            children: [
              TabBar(
                tabs: navController.tabs,
                labelColor: Colors.black,
                unselectedLabelColor: Colors.grey,
                onTap: (index) {
                  navController.changeTab(index);
                },
              ),
              Expanded(
                child: TabBarView(
                  children: navController.tabViews,
                ),
              ),
            ],
          ),
        );
        break;
      case 2:
        tabTitle = '设置';
        content = DefaultTabController(
          length: navController.tabs.length,
          child: Column(
            children: [
              TabBar(
                tabs: navController.tabs,
                labelColor: Colors.black,
                unselectedLabelColor: Colors.grey,
                onTap: (index) {
                  navController.changeTab(index);
                },
              ),
              Expanded(
                child: TabBarView(
                  children: navController.tabViews,
                ),
              ),
            ],
          ),
        );
        break;
      default:
        tabTitle = '未知';
        content = const Center(child: Text('未知内容'));
    }

    tabsController.addTab(tabTitle, content);
  }
}
