/// WBS配置字段来源
enum FieldSourceEnum {
  default_(1, '默认'),
  custom(2, '自定义');

  final int value;
  final String label;

  const FieldSourceEnum(this.value, this.label);

  /// 根据值查找对应的枚举
  static FieldSourceEnum? fromValue(int value) {
    for (var type in FieldSourceEnum.values) {
      if (type.value == value) {
        return type;
      }
    }
    return null;
  }

  /// 直接通过数值获取对应的中文描述
  /// 如果未找到匹配的枚举值，返回null
  static String? getLabel(int value) {
    final type = fromValue(value);
    return type?.label;
  }
}
