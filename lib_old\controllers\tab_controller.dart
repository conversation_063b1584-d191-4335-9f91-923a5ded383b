import 'package:flutter/material.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:get/get.dart';

class TabsController extends GetxController with GetSingleTickerProviderStateMixin {
  final tabs = <String>[].obs;
  final tabContents = <Widget>[].obs;
  final currentIndex = 0.obs;
  
  // 添加这两个属性
  late TabController tabController;
  late PageController pageController;
  
  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 2, vsync: this);
    pageController = PageController();
  }
  
  @override
  void onClose() {
    tabController.dispose();
    pageController.dispose();
    super.onClose();
  }

  // 添加新标签页
  void addTab(String title, Widget content) {
    // 检查是否已存在相同标题的标签页
    final existingIndex = tabs.indexWhere((tab) => tab == title);

    if (existingIndex >= 0) {
      // 如果已存在，切换到该标签页
      currentIndex.value = existingIndex;
    } else {
      // 否则添加新标签页
      tabs.add(title);
      tabContents.add(content);
      currentIndex.value = tabs.length - 1;
    }
  }

  // 关闭标签页
  void closeTab(int index) {
    if (index < 0 || index >= tabs.length) return;

    // 移除标签页
    tabs.removeAt(index);
    tabContents.removeAt(index);

    // 调整当前索引
    if (tabs.isEmpty) {
      currentIndex.value = 0;
    } else if (index <= currentIndex.value) {
      // 如果关闭的是当前标签页或其前面的标签页，需要调整索引
      currentIndex.value = currentIndex.value > 0 ? currentIndex.value - 1 : 0;
    }
  }

  // 切换标签页
  void changeTab(int index) {
    if (index >= 0 && index < tabs.length) {
      currentIndex.value = index;
    }
  }
}
