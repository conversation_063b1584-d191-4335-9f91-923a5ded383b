import 'dart:io';
import 'package:flutter/material.dart';
import 'package:tray_manager/tray_manager.dart';
import 'package:window_manager/window_manager.dart';
import 'package:octasync_client/services/window_service.dart';

/// 系统托盘服务类，用于管理系统托盘图标和菜单
class TrayService {
  TrayService._internal();
  static final TrayService _instance = TrayService._internal();
  factory TrayService() => _instance;

  /// 是否已初始化
  bool _isInitialized = false;

  /// 初始化系统托盘
  Future<void> init() async {
    if (_isInitialized) return;

    // 在Windows系统上使用.ico文件，在其他系统上使用.png文件
    //TODO:后期替换托盘图标
    String iconPath =
        Platform.isWindows ? 'assets/images/tray_icon.ico' : 'assets/images/tray_icon.png';

    try {
      // 初始化系统托盘图标
      await trayManager.setIcon(iconPath);

      // 设置托盘提示文本
      await trayManager.setToolTip('OctaSync');

      // 创建托盘菜单
      await _setupMenu();

      // 添加事件监听器
      trayManager.addListener(_TrayListener());

      _isInitialized = true;
    } catch (e) {
      debugPrint('系统托盘初始化失败: $e');
    }
  }

  /// 设置托盘菜单
  Future<void> _setupMenu() async {
    final menu = Menu(
      items: [
        // MenuItem.separator(),
        MenuItem(key: 'show_window', label: '打开应用'),
        MenuItem(key: 'exit_app', label: '退出应用'),
      ],
    );

    await trayManager.setContextMenu(menu);
  }

  /// 销毁系统托盘
  Future<void> destroy() async {
    if (!_isInitialized) return;

    await trayManager.destroy();
    _isInitialized = false;
  }
}

/// 系统托盘事件监听器
class _TrayListener with TrayListener {
  @override
  void onTrayIconMouseDown() {
    // 在Windows系统上，点击托盘图标显示窗口
    if (Platform.isWindows) {
      windowManager.show();
    } else {
      // 在其他系统上，点击托盘图标显示菜单
      trayManager.popUpContextMenu();
    }
  }

  @override
  void onTrayIconRightMouseDown() {
    // 右键点击显示菜单
    trayManager.popUpContextMenu();
  }

  @override
  void onTrayMenuItemClick(MenuItem menuItem) async {
    switch (menuItem.key) {
      case 'show_window':
        await windowManager.show();
        break;
      case 'exit_app':
        // 允许关闭应用并退出
        WindowService().setAllowClose(true);
        await WindowService().exitApp();
        break;
    }
  }
}
