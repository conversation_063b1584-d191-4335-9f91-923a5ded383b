import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/src/app_basic_text_field.dart';

class AppBasicTextFieldDemo extends StatefulWidget {
  const AppBasicTextFieldDemo({super.key});

  @override
  State<AppBasicTextFieldDemo> createState() => _AppBasicTextFieldDemoState();
}

class _AppBasicTextFieldDemoState extends State<AppBasicTextFieldDemo> {
  String? _name;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      child: Column(
        children: [
          TextButton(
            onPressed: () {
              setState(() {
                _name = '${_name}1';
              });
            },
            child: Text('组件外部修改'),
          ),
          Container(
            child: Text(
              '标题---${_name}',
              style: TextStyle(color: Colors.black, fontSize: 13, fontWeight: FontWeight.bold),
            ),
          ),
          SizedBox(height: 10),
          AppBasicTextField(
            // controller: _nameController,
            initialValue: _name,
            onChanged: (value) {
              _name = value;
              setState(() {});
            },
            decoration: InputDecoration(
              border: OutlineInputBorder(borderSide: BorderSide(color: Colors.grey, width: 1)),
              contentPadding: EdgeInsets.symmetric(horizontal: 10),
              hintText: '请输入字段名称',
            ),
          ),
        ],
      ),
    );
  }
}
