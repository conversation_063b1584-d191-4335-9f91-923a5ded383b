import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:octasync_client/views/main_layout.dart';

void main() {
  // 确保在运行应用前初始化所有必要的服务
  WidgetsFlutterBinding.ensureInitialized();

  runApp(const MyApp());
  // runApp(GetMaterialApp(
  //   home: TabPage(),
  // ));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'IM Client',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      debugShowCheckedModeBanner: false, // 移除调试标记
      home: MainLayout(),
    );
  }
}
