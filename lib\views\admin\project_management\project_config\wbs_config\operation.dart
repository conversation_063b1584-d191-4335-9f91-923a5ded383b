import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';

/// 操作设置
class Operation extends StatefulWidget {
  const Operation({super.key});

  @override
  State<Operation> createState() => _OperationState();
}

class _OperationState extends State<Operation> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        children: [_buildFunctionConfig(), SizedBox(height: 20), _buildAbnormalConfig()],
      ),
    );
  }

  /// 分割线
  Widget _buildDivider(String title) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(title, style: Theme.of(context).textTheme.labelMedium),
        const SizedBox(width: 10),
        Expanded(child: Divider()),
      ],
    );
  }

  /// 功能配置
  Widget _buildFunctionConfig() {
    bool isAddComponent = false;
    double wbsLevel = 5;
    double taskCount = 5;
    double timeSpan = 5;

    return Column(
      children: [
        _buildDivider('功能配置'),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(border: Border(bottom: BorderSide(color: context.border300))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('默认新增【项目管理】的普通组件'),
                  SizedBox(height: 10),
                  Text(
                    '勾选后，项目创建时自动添加管理模块【项目管理】的标准组件',
                    style: TextStyle(color: AppColors.textDisabled, fontSize: 12),
                  ),
                ],
              ),
              AppSwitch(
                value: isAddComponent,
                onChanged: (value) {
                  setState(() {
                    isAddComponent = value;
                  });
                },
              ),
            ],
          ),
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(border: Border(bottom: BorderSide(color: context.border300))),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text('WBS层级结构深度配置'),
                  SizedBox(width: 10),
                  SizedBox(
                    width: 120,
                    child: AppInputNumber(
                      value: wbsLevel,
                      size: InputNumberSize.small,
                      min: 1,
                      max: 10,
                      onChanged: (value) {
                        setState(() {
                          wbsLevel = value;
                        });
                      },
                    ),
                  ),
                  SizedBox(width: 10),
                  Text('层'),
                ],
              ),
              SizedBox(height: 10),
              Text(
                '该配置决定项目分解的颗粒度上限，超过 10 层将无法创建子节点，可平衡管理精度与复杂度',
                style: TextStyle(color: AppColors.textDisabled, fontSize: 12),
              ),
            ],
          ),
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(border: Border(bottom: BorderSide(color: context.border300))),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text('单个工作包分解/关联任务数量'),
                  SizedBox(width: 10),
                  SizedBox(
                    width: 120,
                    child: AppInputNumber(
                      value: taskCount,
                      size: InputNumberSize.small,
                      min: 1,
                      max: 10,
                      onChanged: (value) {
                        setState(() {
                          taskCount = value;
                        });
                      },
                    ),
                  ),
                  SizedBox(width: 10),
                  Text('个'),
                ],
              ),
              SizedBox(height: 10),
              Text(
                '员工可申请撤销已通过的审批（配置前已通过的审批不可撤销，撤销需填写撤销理由）',
                style: TextStyle(color: AppColors.textDisabled, fontSize: 12),
              ),
            ],
          ),
        ),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text('单个工作包的时间跨度不得超过'),
                  SizedBox(width: 10),
                  SizedBox(
                    width: 120,
                    child: AppInputNumber(
                      value: timeSpan,
                      size: InputNumberSize.small,
                      min: 1,
                      onChanged: (value) {
                        setState(() {
                          timeSpan = value;
                        });
                      },
                    ),
                  ),
                  SizedBox(width: 10),
                  Text('天'),
                ],
              ),
              SizedBox(height: 10),
              Text(
                '规范单个工作包时间周期设置，有助于实现精细化项目管理',
                style: TextStyle(color: AppColors.textDisabled, fontSize: 12),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 异常标识配置
  Widget _buildAbnormalConfig() {
    return Column(
      children: [
        _buildDivider('异常标识配置'),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(vertical: 10),
          decoration: BoxDecoration(border: Border(bottom: BorderSide(color: context.border300))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('工作包中的任务异常标识'),
                  SizedBox(height: 10),
                  Text(
                    '自动识别工作包中的任务有无异常状态，高效展示异常问题及位置',
                    style: TextStyle(color: AppColors.textDisabled, fontSize: 12),
                  ),
                  SizedBox(height: 10),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    spacing: 5,
                    children: [
                      AppTag(text: '已滞后', color: AppColors.error),
                      AppTag(text: '创建复核不通过', color: AppColors.warning),
                      AppTag(text: '变更复核不通过', color: AppColors.warning),
                    ],
                  ),
                ],
              ),
              Icon(IconFont.xianxing_bianji, size: AppIconSize.small, color: context.icon300),
            ],
          ),
        ),
      ],
    );
  }
}
