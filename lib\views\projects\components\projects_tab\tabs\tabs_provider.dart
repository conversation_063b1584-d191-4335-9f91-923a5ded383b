import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class TabsProvider with ChangeNotifier {
  ///
  /// 私有变量区域
  ///
  int _checkedId = 1; // 默认选中
  final List<TabItem> _items = [
    TabItem(id: 1, title: '北京aaa121231231'),
    TabItem(id: 2, title: '上海bbb123123123123123'),
    TabItem(id: 3, title: '广州ccc123123123'),
    TabItem(id: 4, title: '深圳123123123'),
    // TabItem(id: 5, title: '杭州'),
    // TabItem(id: 6, title: '苏州'),
    // TabItem(id: 7, title: '成都'),
    // TabItem(id: 8, title: '北京'),
    // TabItem(id: 9, title: '武汉'),
    // TabItem(id: 10, title: '郑州'),
    // TabItem(id: 11, title: '洛阳'),
    // TabItem(id: 12, title: '厦门'),
    // TabItem(id: 13, title: '青岛'),
    // TabItem(id: 14, title: '拉萨'),
  ];

  ///
  /// 公开属性区域
  ///
  List<TabItem> get items => _items;
  get currentCheckedId => _checkedId;
  get currentCheckedItem => _items.firstWhere((element) => element.id == _checkedId);

  ///
  /// 方法区域
  ///
  changeCheckedId(int id) {
    _checkedId = id;
    notifyListeners();
  }

  appendItem(TabItem item) {
    print('添加新项目');
    _items.add(item);
    notifyListeners();
  }

  removeItem(int id) {
    _items.removeWhere((element) => element.id == id);
    notifyListeners();
  }
}
