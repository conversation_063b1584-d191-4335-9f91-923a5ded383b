import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/message_controller.dart';
import 'message_list_view.dart';

class UnreadMessageView extends GetView<MessageController> {
  const UnreadMessageView({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.unreadMessages.isEmpty) {
        return const Center(
          child: Text('没有未读消息'),
        );
      }
      
      return ListView.builder(
        itemCount: controller.unreadMessages.length,
        itemBuilder: (context, index) {
          final message = controller.unreadMessages[index];
          return MessageListTile(message: message);
        },
      );
    });
  }
}
