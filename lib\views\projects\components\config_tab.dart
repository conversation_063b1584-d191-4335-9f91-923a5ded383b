import 'package:flutter/material.dart';
import 'package:octasync_client/views/projects/components/projects_tab/tab_title.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class ConfigTab extends StatefulWidget {
  const ConfigTab({super.key});

  @override
  State<ConfigTab> createState() => _ConfigTabState();
}

class _ConfigTabState extends State<ConfigTab> {
  final double titleHeight = Variables.titleHeight;

  @override
  Widget build(BuildContext context) {
    var borderColor = Variables.commonBorderColor(context);

    return Scaffold(
      body: Column(
        children: [
          Container(
            height: titleHeight,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: borderColor, // 边框颜色
                  width: 1.0, // 边框宽度
                ),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.only(left: 5, right: 5),
              child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [TabTitle()]),
            ),
          ),
        ],
      ),
    );
  }
}
