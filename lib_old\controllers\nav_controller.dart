import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../views/message_list_view.dart';
import '../views/unread_message_view.dart';

class NavController extends GetxController
    with GetTickerProviderStateMixin {
  final _currentIndex = 0.obs;
  final _tabs = <Tab>[].obs;
  final _tabViews = <Widget>[].obs;
  TabController? tabController;

  int get currentIndex => _currentIndex.value;
  List<Tab> get tabs => _tabs;
  List<Widget> get tabViews => _tabViews;

  @override
  void onInit() {
    super.onInit();
    // 初始化默认标签页
    _tabs.value = [
      const Tab(text: '所有消息'),
      const Tab(text: '未读消息'),
    ];
    _tabViews.value = [
      const MessageListView(),
      const UnreadMessageView(),
    ];
    // 初始化 TabController
    tabController = TabController(length: _tabs.length, vsync: this);
  }

  void changeTab(int index) {
    _currentIndex.value = index;
  }

  void changePage(int index) {
    _currentIndex.value = index;

    List<Tab> newTabs;
    List<Widget> newViews;

    switch (index) {
      case 0:
        newTabs = [
          const Tab(text: '所有消息'),
          const Tab(text: '未读消息'),
        ];
        newViews = [
          const MessageListView(),
          const UnreadMessageView(),
        ];
        break;
      case 1:
        newTabs = [
          const Tab(text: '好友列表'),
          const Tab(text: '群组'),
        ];
        newViews = [
          const Center(child: Text('好友列表内容')),
          const Center(child: Text('群组内容')),
        ];
        break;
      case 2:
        newTabs = [
          const Tab(text: '个人设置'),
          const Tab(text: '系统设置'),
        ];
        newViews = [
          const Center(child: Text('个人设置内容')),
          const Center(child: Text('系统设置内容')),
        ];
        break;
      default:
        return;
    }

    // 更新标签页
    _updateTabs(newTabs, newViews);
  }

  void _updateTabs(List<Tab> newTabs, List<Widget> newViews) {
    // 先创建新的 controller
    final newController = TabController(length: newTabs.length, vsync: this);
    
    // 保存对旧 controller 的引用
    final oldController = tabController;
    
    // 更新数据
    _tabs.value = newTabs;
    _tabViews.value = newViews;
    
    // 先设置新的 controller，再处理旧的
    tabController = newController;
    
    // 通知 UI 更新
    update();
    
    // 最后安全地处理旧的 controller
    if (oldController != null) {
      // 使用 Future.microtask 确保在当前帧渲染完成后再销毁
      Future.microtask(() {
        oldController.dispose();
      });
    }
  }

  @override
  void onClose() {
    tabController?.dispose();
    super.onClose();
  }
}
