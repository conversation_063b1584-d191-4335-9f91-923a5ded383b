import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_tree.dart';
import 'package:octasync_client/components/selector/employee_selector/employee_selector_provider.dart';
import 'package:octasync_client/imports.dart';

/// 员工选择对话框内容
class EmployeeSelectorDialog extends StatelessWidget {
  final GlobalKey<EmployeeTreeState> employeeSelectorKey;

  const EmployeeSelectorDialog({super.key, required this.employeeSelectorKey});

  /// 更新选中的员工列表
  void _updateCheckedEmployees(BuildContext context) {
    final checkedEmployees = employeeSelectorKey.currentState?.getAllCheckedEmployees() ?? [];
    context.read<EmployeeSelectorProvider>().updateCheckedEmployees(checkedEmployees);
  }

  @override
  Widget build(BuildContext context) {
    // 重置状态到默认选中
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<EmployeeSelectorProvider>().resetToDefault();
    });

    return Row(
      children: [
        // 左侧员工树状
        Expanded(
          child: Container(
            width: 250,
            decoration: BoxDecoration(border: Border(right: BorderSide(color: context.border300))),
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    border: Border(bottom: BorderSide(color: context.border300)),
                  ),
                  child: AppInput(
                    hintText: '搜索',
                    size: InputSize.medium,
                    showErrMsg: false,
                    onChanged: (value) {
                      employeeSelectorKey.currentState?.setSearchQuery(value);
                      context.read<EmployeeSelectorProvider>().setSearchQuery(value);
                    },
                  ),
                ),
                Expanded(
                  child: EmployeeTree(
                    key: employeeSelectorKey,
                    showCheckbox: true,
                    onNodeSelected: (employee, value) {
                      _updateCheckedEmployees(context);
                    },
                    onDataLoaded: () {
                      // 数据加载完成后展开顶级部门并应用默认选中状态
                      employeeSelectorKey.currentState?.expandTopLevelDepartments();
                      context.read<EmployeeSelectorProvider>().applyDefaultCheckedState(
                        employeeSelectorKey,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
        // 右侧已选列表
        Expanded(
          child: Consumer<EmployeeSelectorProvider>(
            builder: (context, provider, child) {
              final checkedEmployees = provider.checkedEmployees;
              return Padding(
                padding: const EdgeInsets.all(10),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('已选择(${provider.checkedCount})'),
                        AppButton(
                          text: '清空全部',
                          type: ButtonType.primary,
                          textOnly: true,
                          onPressed: () {
                            employeeSelectorKey.currentState?.resetAllNodesCheck();
                            provider.clearAllCheckedEmployees();
                          },
                        ),
                      ],
                    ),
                    Divider(color: context.border300),
                    Expanded(
                      child: ListView.builder(
                        itemCount: checkedEmployees.length,
                        itemBuilder: (context, index) {
                          final employee = checkedEmployees[index];
                          return Padding(
                            padding: const EdgeInsets.only(top: 5, bottom: 5, right: 10),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(employee.name, overflow: TextOverflow.ellipsis),
                                      if (employee.parentName.isNotEmpty)
                                        Text(
                                          employee.parentName,
                                          style: TextStyle(color: AppColors.textHint, fontSize: 12),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                    ],
                                  ),
                                ),
                                MouseRegion(
                                  cursor: SystemMouseCursors.click,
                                  child: GestureDetector(
                                    onTap: () {
                                      employeeSelectorKey.currentState?.uncheckNode(employee.id!);
                                      provider.removeCheckedEmployee(employee.id!);
                                    },
                                    child: Icon(
                                      IconFont.mianxing_jianshao,
                                      color: AppColors.error,
                                      size: AppIconSize.medium,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
