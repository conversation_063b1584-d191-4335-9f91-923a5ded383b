import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class DropdownExamplePage extends StatefulWidget {
  const DropdownExamplePage({super.key});

  @override
  State<DropdownExamplePage> createState() => _DropdownExamplePageState();
}

class _DropdownExamplePageState extends State<DropdownExamplePage> {
  // 用于存储各个示例的选中值
  Map<String, dynamic> selectedValues = {};

  // 通用的下拉菜单项列表
  List<DropdownItem> _getBasicItems() {
    return [
      DropdownItem(text: '选项 1', value: 'option1'),
      DropdownItem(text: '选项 2', value: 'option2'),
      DropdownItem(text: '选项 3', value: 'option3'),
    ];
  }

  // 带图标的下拉菜单项
  List<DropdownItem> _getItemsWithIcons() {
    return [
      DropdownItem(text: '编辑', value: 'edit', iconData: Icons.edit),
      DropdownItem(text: '删除', value: 'delete', iconData: Icons.delete),
      DropdownItem(text: '分享', value: 'share', iconData: Icons.share),
    ];
  }

  // 带分隔线的下拉菜单项
  List<DropdownItem> _getItemsWithDivider() {
    return [
      DropdownItem(text: '复制', value: 'copy', iconData: Icons.copy),
      DropdownItem(text: '剪切', value: 'cut', iconData: Icons.cut),
      DropdownItem(text: '粘贴', value: 'paste', iconData: Icons.paste, divided: true),
      DropdownItem(text: '全选', value: 'selectAll', iconData: Icons.select_all),
    ];
  }

  // 禁用部分项的下拉菜单
  List<DropdownItem> _getItemsWithDisabled() {
    return [
      DropdownItem(text: '可用选项', value: 'enabled'),
      DropdownItem(text: '禁用选项', value: 'disabled', disabled: true),
      DropdownItem(text: '另一个可用选项', value: 'anotherEnabled'),
    ];
  }

  // 超长文本的下拉菜单项
  List<DropdownItem> _getLongTextItems() {
    return [
      DropdownItem(text: '这是一个非常长的选项文本，用于测试自动宽度功能', value: 'longText1'),
      DropdownItem(text: '另一个长文本选项，用于测试下拉菜单宽度自适应', value: 'longText2'),
      DropdownItem(text: '短选项', value: 'shortText'),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('下拉菜单示例')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 120),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('AppDropdown 组件示例', style: Theme.of(context).textTheme.headlineMedium),
            const SizedBox(height: 8),
            const Text('本页面展示了AppDropdown组件的各种用法和参数效果，帮助开发者了解如何正确使用此组件。'),

            _buildBasicExamples(),
            _buildAutoWidthExamples(),
            _buildTriggerExamples(),
            _buildSizeExamples(),
            _buildTypeExamples(),
            _buildPlacementExamples(),
            _buildMorePropertiesExamples(),
            _buildCustomItemsExamples(),
            _buildWidthExamples(),
          ],
        ),
      ),
    );
  }

  Widget _buildBasicExamples() {
    return _buildExampleSection('基本用法', 'AppDropdown的最基本用法，展示带有下拉菜单的按钮。', [
      AppDropdown(
        text: '基本下拉菜单',
        items: _getBasicItems(),
        onItemSelected: (item) {
          setState(() {
            selectedValues['basic'] = item.value;
          });
        },
        value: selectedValues['basic'],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [const SizedBox(height: 8), Text('选中值: ${selectedValues['basic'] ?? '无'}')],
      ),
    ]);
  }

  Widget _buildAutoWidthExamples() {
    return _buildExampleSection('下拉面板自动宽度', '下拉面板根据items自动适应宽度。', [
      AppDropdown(
        size: DropdownSize.small,
        items: [
          DropdownItem(text: '选项1', value: 1, disabled: true, iconData: Icons.settings),
          DropdownItem(text: '选项2', value: 2, iconData: Icons.settings, divided: true),
          DropdownItem(text: '选项选项选项选项3', value: 3, iconData: IconFont.mianxing_huibao),
        ],
        text: '选择选项',
        trigger: DropdownTrigger.click, // 可选hover或click
        child: AppButton(
          iconData: Icons.star_rounded,
          size: ButtonSize.small,
          color: AppColors.yellow,
          type: ButtonType.transparent,
          onPressed: () {},
        ),
      ),
    ]);
  }

  Widget _buildTriggerExamples() {
    return _buildExampleSection('触发方式 (trigger)', '下拉菜单可以通过点击(click)或悬停(hover)两种方式触发显示。', [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('点击触发 (默认)'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '点击打开',
            items: _getBasicItems(),
            trigger: DropdownTrigger.click,
            onItemSelected: (item) {
              setState(() {
                selectedValues['click'] = item.value;
              });
            },
            value: selectedValues['click'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('悬停触发'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '悬停打开',
            items: _getBasicItems(),
            trigger: DropdownTrigger.hover,
            onItemSelected: (item) {
              setState(() {
                selectedValues['hover'] = item.value;
              });
            },
            value: selectedValues['hover'],
          ),
        ],
      ),
    ]);
  }

  Widget _buildSizeExamples() {
    return _buildExampleSection('尺寸 (size)', 'AppDropdown提供四种尺寸：large, medium (默认), small, mini。', [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Large'),
          const SizedBox(height: 8),
          AppDropdown(
            text: 'Large 尺寸',
            items: _getBasicItems(),
            size: DropdownSize.large,
            onItemSelected: (item) {
              setState(() {
                selectedValues['sizeLarge'] = item.value;
              });
            },
            value: selectedValues['sizeLarge'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Medium (默认)'),
          const SizedBox(height: 8),
          AppDropdown(
            text: 'Medium 尺寸',
            items: _getBasicItems(),
            size: DropdownSize.medium,
            onItemSelected: (item) {
              setState(() {
                selectedValues['sizeMedium'] = item.value;
              });
            },
            value: selectedValues['sizeMedium'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Small'),
          const SizedBox(height: 8),
          AppDropdown(
            text: 'Small 尺寸',
            items: _getBasicItems(),
            size: DropdownSize.small,
            onItemSelected: (item) {
              setState(() {
                selectedValues['sizeSmall'] = item.value;
              });
            },
            value: selectedValues['sizeSmall'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('Mini'),
          const SizedBox(height: 8),
          AppDropdown(
            text: 'Mini 尺寸',
            items: _getBasicItems(),
            size: DropdownSize.mini,
            onItemSelected: (item) {
              setState(() {
                selectedValues['sizeMini'] = item.value;
              });
            },
            value: selectedValues['sizeMini'],
          ),
        ],
      ),
    ]);
  }

  Widget _buildTypeExamples() {
    return _buildExampleSection(
      '类型 (type)',
      'AppDropdown提供多种类型：primary, success, warning, danger, info, default_(默认), transparent。',
      [
        AppDropdown(
          text: 'Primary',
          items: _getBasicItems(),
          type: DropdownType.primary,
          onItemSelected: (item) {
            setState(() {
              selectedValues['typePrimary'] = item.value;
            });
          },
          value: selectedValues['typePrimary'],
        ),
        AppDropdown(
          text: 'Success',
          items: _getBasicItems(),
          type: DropdownType.success,
          onItemSelected: (item) {
            setState(() {
              selectedValues['typeSuccess'] = item.value;
            });
          },
          value: selectedValues['typeSuccess'],
        ),
        AppDropdown(
          text: 'Warning',
          items: _getBasicItems(),
          type: DropdownType.warning,
          onItemSelected: (item) {
            setState(() {
              selectedValues['typeWarning'] = item.value;
            });
          },
          value: selectedValues['typeWarning'],
        ),
        AppDropdown(
          text: 'Danger',
          items: _getBasicItems(),
          type: DropdownType.danger,
          onItemSelected: (item) {
            setState(() {
              selectedValues['typeDanger'] = item.value;
            });
          },
          value: selectedValues['typeDanger'],
        ),
        AppDropdown(
          text: 'Info',
          items: _getBasicItems(),
          type: DropdownType.info,
          onItemSelected: (item) {
            setState(() {
              selectedValues['typeInfo'] = item.value;
            });
          },
          value: selectedValues['typeInfo'],
        ),
        AppDropdown(
          text: 'Default',
          items: _getBasicItems(),
          type: DropdownType.default_,
          onItemSelected: (item) {
            setState(() {
              selectedValues['typeDefault'] = item.value;
            });
          },
          value: selectedValues['typeDefault'],
        ),
        AppDropdown(
          text: 'Transparent',
          items: _getBasicItems(),
          type: DropdownType.transparent,
          onItemSelected: (item) {
            setState(() {
              selectedValues['typeTransparent'] = item.value;
            });
          },
          value: selectedValues['typeTransparent'],
        ),
      ],
    );
  }

  Widget _buildPlacementExamples() {
    return _buildExampleSection('位置 (placement)', 'AppDropdown支持12种下拉菜单位置。以下展示几个常用位置。', [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('BottomLeft (默认)'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '下左',
            items: _getBasicItems(),
            placement: DropdownPlacement.bottomLeft,
            onItemSelected: (item) {
              setState(() {
                selectedValues['placementBottomLeft'] = item.value;
              });
            },
            value: selectedValues['placementBottomLeft'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('BottomRight'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '下右',
            items: _getBasicItems(),
            placement: DropdownPlacement.bottomRight,
            onItemSelected: (item) {
              setState(() {
                selectedValues['placementBottomRight'] = item.value;
              });
            },
            value: selectedValues['placementBottomRight'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('TopLeft'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '上左',
            items: _getBasicItems(),
            placement: DropdownPlacement.topLeft,
            onItemSelected: (item) {
              setState(() {
                selectedValues['placementTopLeft'] = item.value;
              });
            },
            value: selectedValues['placementTopLeft'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('RightTop'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '右上',
            items: _getBasicItems(),
            placement: DropdownPlacement.rightTop,
            onItemSelected: (item) {
              setState(() {
                selectedValues['placementRightTop'] = item.value;
              });
            },
            value: selectedValues['placementRightTop'],
          ),
        ],
      ),
    ]);
  }

  Widget _buildMorePropertiesExamples() {
    return _buildExampleSection('更多属性', '展示disabled, textOnly, expand等更多属性的效果。', [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('禁用状态 (disabled)'),
          const SizedBox(height: 8),
          AppDropdown(text: '禁用下拉菜单', items: _getBasicItems(), disabled: true),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('文本按钮 (textOnly)'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '文本下拉菜单',
            items: _getBasicItems(),
            textOnly: true,
            type: DropdownType.primary,
            onItemSelected: (item) {
              setState(() {
                selectedValues['textOnly'] = item.value;
              });
            },
            value: selectedValues['textOnly'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('占满宽度 (expand)'),
          const SizedBox(height: 8),
          Container(
            width: 200,
            child: AppDropdown(
              text: '宽度占满',
              items: _getBasicItems(),
              expand: true,
              onItemSelected: (item) {
                setState(() {
                  selectedValues['expand'] = item.value;
                });
              },
              value: selectedValues['expand'],
            ),
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('自定义图标 (iconData)'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '自定义图标',
            iconData: Icons.filter_list,
            items: _getBasicItems(),
            onItemSelected: (item) {
              setState(() {
                selectedValues['customIcon'] = item.value;
              });
            },
            value: selectedValues['customIcon'],
          ),
        ],
      ),
    ]);
  }

  Widget _buildCustomItemsExamples() {
    return _buildExampleSection('自定义下拉项', '展示如何自定义下拉项，包括图标、分隔线、禁用项等。', [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('带图标的下拉项'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '操作',
            items: _getItemsWithIcons(),
            onItemSelected: (item) {
              setState(() {
                selectedValues['withIcons'] = item.value;
              });
            },
            value: selectedValues['withIcons'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('带分隔线的下拉项'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '编辑菜单',
            items: _getItemsWithDivider(),
            onItemSelected: (item) {
              setState(() {
                selectedValues['withDivider'] = item.value;
              });
            },
            value: selectedValues['withDivider'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('带禁用项的下拉菜单'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '禁用部分选项',
            items: _getItemsWithDisabled(),
            onItemSelected: (item) {
              setState(() {
                selectedValues['withDisabled'] = item.value;
              });
            },
            value: selectedValues['withDisabled'],
          ),
        ],
      ),
    ]);
  }

  Widget _buildWidthExamples() {
    return _buildExampleSection('宽度设置', '展示下拉菜单的宽度设置，包括固定宽度和自动宽度。', [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('默认宽度'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '默认宽度',
            items: _getBasicItems(),
            onItemSelected: (item) {
              setState(() {
                selectedValues['defaultWidth'] = item.value;
              });
            },
            value: selectedValues['defaultWidth'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('指定宽度 (dropdownWidth: 300)'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '固定宽度',
            items: _getBasicItems(),
            dropdownWidth: 300,
            onItemSelected: (item) {
              setState(() {
                selectedValues['fixedWidth'] = item.value;
              });
            },
            value: selectedValues['fixedWidth'],
          ),
        ],
      ),
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('自动宽度 (根据内容自动调整)'),
          const SizedBox(height: 8),
          AppDropdown(
            text: '长文本测试',
            items: _getLongTextItems(),
            onItemSelected: (item) {
              setState(() {
                selectedValues['autoWidth'] = item.value;
              });
            },
            value: selectedValues['autoWidth'],
          ),
        ],
      ),
    ]);
  }

  // 辅助方法：用于创建示例部分
  Widget _buildExampleSection(String title, String description, List<Widget> examples) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Text(title, style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 8),
        Text(description),
        const SizedBox(height: 16),
        Wrap(spacing: 16, runSpacing: 16, children: examples),
        const Divider(height: 40),
      ],
    );
  }
}
