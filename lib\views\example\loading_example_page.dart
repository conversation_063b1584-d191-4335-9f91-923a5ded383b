import 'dart:async';
import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// Loading组件示例页面
class LoadingExamplePage extends StatefulWidget {
  const LoadingExamplePage({super.key});

  @override
  State<LoadingExamplePage> createState() => _LoadingExamplePageState();
}

class _LoadingExamplePageState extends State<LoadingExamplePage> {
  // 控制局部loading状态
  bool _isLoading1 = false;
  bool _isLoading2 = false;
  bool _isLoading3 = false;

  // 模拟异步请求
  Future<void> _simulateRequest({
    Duration duration = const Duration(seconds: 2),
    bool showError = false,
  }) async {
    await Future.delayed(duration);
    if (showError) {
      throw Exception('模拟请求失败');
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('基础用法'),
          _buildBasicExample(),

          _buildSectionTitle('局部加载'),
          _buildLocalLoadingExample(),

          _buildSectionTitle('全局加载'),
          _buildGlobalLoadingExample(),

          _buildSectionTitle('异步任务处理'),
          _buildAsyncExample(),

          _buildSectionTitle('多区域并发加载'),
          _buildMultiAreaExample(),
        ],
      ),
    );
  }

  // 构建章节标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 16),
      child: Text(title, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
    );
  }

  // 构建示例容器
  Widget _buildExampleContainer({required Widget child, double? height}) {
    return Container(
      width: double.infinity,
      height: height,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: context.border200),
        borderRadius: BorderRadius.circular(AppRadiusSize.radius8),
      ),
      child: child,
    );
  }

  // 基础示例
  Widget _buildBasicExample() {
    return _buildExampleContainer(
      height: 200,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const AppLoading(isLoading: true, text: "加载中..."),
          const SizedBox(height: 32),
          Text('最基础的Loading组件', style: TextStyle(color: context.textSecondary)),
        ],
      ),
    );
  }

  // 局部加载示例
  Widget _buildLocalLoadingExample() {
    return _buildExampleContainer(
      height: 450,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('局部加载示例', style: TextStyle(color: context.textSecondary)),
          const SizedBox(height: 16),
          AppLoading(
            isLoading: _isLoading1,
            child: Container(
              width: 800,
              height: 300,
              decoration: BoxDecoration(
                color: context.background200,
                borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
              ),
              child: Center(
                child: ElevatedButton(
                  onPressed: () {
                    debugPrint('点击内容区域');
                  },
                  child: const Text('内容区域'),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          AppButton(
            text: _isLoading1 ? '关闭加载' : '显示加载',
            type: ButtonType.primary,
            onPressed: () {
              setState(() {
                _isLoading1 = !_isLoading1;
              });
            },
          ),
        ],
      ),
    );
  }

  // 全局加载示例
  Widget _buildGlobalLoadingExample() {
    return _buildExampleContainer(
      height: 150,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('点击按钮显示全局加载', style: TextStyle(color: context.textSecondary)),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppButton(
                text: '显示 3 秒',
                type: ButtonType.primary,
                onPressed: () {
                  LoadingManager.showLoading(text: "加载中...", duration: const Duration(seconds: 3));
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 异步任务示例
  Widget _buildAsyncExample() {
    return _buildExampleContainer(
      height: 150,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('异步任务自动显示/隐藏加载', style: TextStyle(color: context.textSecondary)),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              AppButton(
                text: '模拟成功请求',
                type: ButtonType.primary,
                onPressed: () async {
                  await LoadingManager.during(() => _simulateRequest(), text: "处理中...");
                  ToastManager.success('请求成功');
                },
              ),
              const SizedBox(width: 16),
              AppButton(
                text: '模拟失败请求',
                type: ButtonType.danger,
                onPressed: () async {
                  try {
                    await LoadingManager.during(
                      () => _simulateRequest(showError: true),
                      text: "处理中...",
                    );
                  } catch (e) {
                    ToastManager.error('请求失败');
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 多区域并发加载示例
  Widget _buildMultiAreaExample() {
    return _buildExampleContainer(
      height: 550,
      child: Column(
        children: [
          Text('模拟一个页面中的多个独立加载区域', style: TextStyle(color: context.textSecondary)),
          const SizedBox(height: 24),

          // 第一个区域
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 120,
                  decoration: BoxDecoration(
                    color: context.background200,
                    borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
                  ),
                  child: AppLoading(
                    isLoading: _isLoading1,
                    text: "用户数据加载中",
                    child: const Center(child: Text('用户基本信息区域')),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              AppButton(
                text: _isLoading1 ? '关闭' : '加载',
                type: ButtonType.primary,
                onPressed: () async {
                  if (!_isLoading1) {
                    setState(() => _isLoading1 = true);
                    await Future.delayed(const Duration(seconds: 2));
                    setState(() => _isLoading1 = false);
                  } else {
                    setState(() => _isLoading1 = false);
                  }
                },
              ),
            ],
          ),

          const SizedBox(height: 24),

          // 第二个区域
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 120,
                  decoration: BoxDecoration(
                    color: context.background200,
                    borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
                  ),
                  child: AppLoading(
                    isLoading: _isLoading2,
                    text: "订单列表加载中",
                    child: const Center(child: Text('用户订单列表区域')),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              AppButton(
                text: _isLoading2 ? '关闭' : '加载',
                type: ButtonType.primary,
                onPressed: () async {
                  if (!_isLoading2) {
                    setState(() => _isLoading2 = true);
                    await Future.delayed(const Duration(seconds: 3));
                    setState(() => _isLoading2 = false);
                  } else {
                    setState(() => _isLoading2 = false);
                  }
                },
              ),
            ],
          ),

          const SizedBox(height: 24),

          // 第三个区域
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 120,
                  decoration: BoxDecoration(
                    color: context.background200,
                    borderRadius: BorderRadius.circular(AppRadiusSize.radius6),
                  ),
                  child: AppLoading(
                    isLoading: _isLoading3,
                    text: "消息加载中",
                    child: const Center(child: Text('用户消息区域')),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              AppButton(
                text: _isLoading3 ? '关闭' : '加载',
                type: ButtonType.primary,
                onPressed: () async {
                  if (!_isLoading3) {
                    setState(() => _isLoading3 = true);
                    await Future.delayed(const Duration(seconds: 4));
                    setState(() => _isLoading3 = false);
                  } else {
                    setState(() => _isLoading3 = false);
                  }
                },
              ),
            ],
          ),

          const SizedBox(height: 24),

          // 同时加载所有区域
          AppButton(
            text: '同时加载全部区域',
            type: ButtonType.primary,
            onPressed: () async {
              setState(() {
                _isLoading1 = true;
                _isLoading2 = true;
                _isLoading3 = true;
              });

              // 模拟不同完成时间
              Future.delayed(const Duration(seconds: 2), () {
                setState(() => _isLoading1 = false);
              });
              Future.delayed(const Duration(seconds: 4), () {
                setState(() => _isLoading2 = false);
              });
              Future.delayed(const Duration(seconds: 6), () {
                setState(() => _isLoading3 = false);
              });
            },
          ),
        ],
      ),
    );
  }
}
