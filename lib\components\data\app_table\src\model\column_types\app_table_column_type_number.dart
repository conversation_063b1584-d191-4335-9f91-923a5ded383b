import 'package:octasync_client/components/data/app_table/src/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/src/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column_type_has_format.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column_type_with_number_format.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column_type_with_request_field.dart';
import 'package:intl/intl.dart' as intl;

import 'package:json_annotation/json_annotation.dart';

part 'app_table_column_type_number.g.dart';

@JsonSerializable()
class AppTableColumnTypeNumber
    with
        AppTableColumnTypeDefaultMixin,
        AppTableColumnTypeWithNumberFormat,
        AppTableColumnTypeWithRequestField
    implements AppTableColumnType, AppTableColumnTypeHasFormat<String> {
  dynamic _defaultValue;
  @override
  dynamic get defaultValue => _defaultValue;
  @override
  set defaultValue(dynamic value) => _defaultValue = value;

  @override
  bool isSaveRequired = false;

  @override
  bool isSubmitRequired = false;

  String _columnDesc;
  @override
  String get columnDesc => _columnDesc;
  @override
  set columnDesc(String value) => _columnDesc = value;

  @override
  ColumnTypeEnum typeCode = ColumnTypeEnum.number;
  // @override
  // final intl.NumberFormat numberFormat;
  @override
  intl.NumberFormat get numberFormat {
    return intl.NumberFormat(
      AppTableGeneralHelper.getFormatStr(
        isShowPercentiles: isShowPercentiles,
        precision: precision,
        isRetainDecimal: isRetainDecimal,
      ),
      locale,
    );
  }

  @override
  final bool negative;

  @override
  final int decimalPoint;

  @override
  final bool allowFirstDot;

  @override
  final String? locale;

  @override
  final String format;

  @override
  final bool applyFormatOnInit;

  // @override
  // int precision = 0;

  // @override
  // bool isShowPercentiles = false;

  // /// 精度
  int _precision = 0;
  // /// 是否显示千分位
  bool _isShowPercentiles = false;

  /// 精度
  @override
  int get precision => _precision;
  set precision(int value) => _precision = value;

  /// 是否显示千分位
  @override
  bool get isShowPercentiles => _isShowPercentiles;
  set isShowPercentiles(bool value) => _isShowPercentiles = value;

  @override
  final bool isRetainDecimal = true;

  AppTableColumnTypeNumber({
    dynamic defaultValue,
    String columnDesc = '',
    required this.negative,
    required this.format,
    required this.applyFormatOnInit,
    required this.allowFirstDot,
    required this.locale,
    int precision = 0,
    bool isShowPercentiles = false,
  }) : _defaultValue = defaultValue,
       //  numberFormat = intl.NumberFormat(
       //    AppTableGeneralHelper.getFormatStr(
       //      isShowPercentiles: isShowPercentiles,
       //      precision: precision,
       //      isRetainDecimal: true,
       //    ),
       //    locale,
       //  ),

       //  numberFormat = AppTableGeneralHelper.getFormatStr(
       //    isShowPercentiles: isShowPercentiles,
       //    precision: precision,
       //    isRetainDecimal: true,
       //  ),
       _columnDesc = columnDesc,
       _precision = precision,
       _isShowPercentiles = isShowPercentiles,
       decimalPoint = _getDecimalPoint(format);

  static int _getDecimalPoint(String format) {
    final int dotIndex = format.indexOf('.');
    return dotIndex < 0 ? 0 : format.substring(dotIndex).length - 1;
  }

  factory AppTableColumnTypeNumber.fromJson(Map<String, dynamic> json) =>
      _$AppTableColumnTypeNumberFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AppTableColumnTypeNumberToJson(this);

  // static intl.NumberFormat _getFormat(AppTableColumnTypeNumber obj) {
  //   return intl.NumberFormat(
  //     AppTableGeneralHelper.getFormatStr(
  //       isShowPercentiles: obj.isShowPercentiles,
  //       precision: obj.precision,
  //       isRetainDecimal: true,
  //     ),
  //     locale,
  //   );
  // }
}
