import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/src/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/src/enums/cell_state_enum.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_checkbox.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_currency.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_date.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_hyperlink.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_number.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_percentage.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_single_select.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_telephone.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_text.dart';
import 'package:octasync_client/components/data/app_table/src/state/app_table_state_manage.dart';
import 'package:octasync_client/components/data/app_table/src/ui/cells/checkbox_cell.dart';
import 'package:octasync_client/components/data/app_table/src/ui/cells/date_cell.dart';
import 'package:octasync_client/components/data/app_table/src/ui/cells/hyperlink_cell.dart';
import 'package:octasync_client/components/data/app_table/src/ui/cells/number_cell.dart';
import 'package:octasync_client/components/data/app_table/src/ui/cells/select_cell.dart';
import 'package:octasync_client/components/data/app_table/src/ui/cells/telephone_cell.dart';
import 'package:octasync_client/components/data/app_table/src/ui/cells/text_cell.dart';
import 'package:provider/provider.dart';

class AppTableCell extends StatefulWidget {
  const AppTableCell({
    super.key,
    this.state = CellStateEnum.normal,
    required this.column,
    required this.rowData,
    required this.rowIdx,
    required this.columnIdx,
    required this.rowId,
    required this.columnId,
  });

  final CellStateEnum state;

  final AppTableColumn column;

  final Map<String, dynamic> rowData;

  final int rowIdx;

  final int columnIdx;

  final String rowId;
  final String columnId;

  @override
  State<AppTableCell> createState() => _AppTableCellState();
}

class _AppTableCellState extends State<AppTableCell> {
  @override
  Widget build(BuildContext context) {
    final editRowId = context.select<AppTableStateManage, String?>((state) => state.editRowId);
    final editColumnId = context.select<AppTableStateManage, String?>(
      (state) => state.editColumnId,
    );

    var isEdit = editRowId == widget.rowId && editColumnId == widget.columnId;

    return _buildCellWidget(isEdit);

    // return Selector<
    //   AppTableStateManage,
    //   ({String? editRowId, String? editColumnId})
    // >(
    //   selector:
    //       (context, provider) => (
    //         editRowId: provider.editRowId,
    //         editColumnId: provider.editColumnId,
    //       ),
    //   builder: (context, data, child) {
    //     var isEdit =
    //         data.editRowId == widget.rowId &&
    //         data.editColumnId == widget.columnId;
    //     return _buildCellWidget(isEdit);
    //   },
    // );
  }

  Widget _buildCellWidget(bool isEdit) {
    Widget cell;

    var columnType = widget.column.type;
    // print('column==${widget.column} rowData==${widget.rowData}');
    // print('build-cell___________________${columnType}');
    // print('');

    // print('widget.rowIdx====${widget.rowIdx}');
    // print('widget.columnIdx=====${widget.columnIdx}');

    if (columnType is AppTableColumnTypeNumber) {
      cell = NumberCell<AppTableColumnTypeNumber>(
        state: isEdit ? CellStateEnum.edit : CellStateEnum.normal,
        column: widget.column,
        rowData: widget.rowData,
        rowIdx: widget.rowIdx,
        columnIdx: widget.columnIdx,
        rowId: widget.rowId,
        columnId: widget.columnId,
      );
    } else if (columnType is AppTableColumnTypePercentage) {
      cell = NumberCell<AppTableColumnTypePercentage>(
        state: isEdit ? CellStateEnum.edit : CellStateEnum.normal,
        column: widget.column,
        rowData: widget.rowData,
        rowIdx: widget.rowIdx,
        columnIdx: widget.columnIdx,
        rowId: widget.rowId,
        columnId: widget.columnId,
      );
    } else if (columnType is AppTableColumnTypeCurrency) {
      cell = NumberCell<AppTableColumnTypeCurrency>(
        state: isEdit ? CellStateEnum.edit : CellStateEnum.normal,
        column: widget.column,
        rowData: widget.rowData,
        rowIdx: widget.rowIdx,
        columnIdx: widget.columnIdx,
        rowId: widget.rowId,
        columnId: widget.columnId,
      );
    } else if (columnType is AppTableColumnTypeCheckbox) {
      cell = CheckboxCell(
        // state: isEdit ? CellStateEnum.edit : CellStateEnum.normal,
        column: widget.column,
        rowData: widget.rowData,
        rowIdx: widget.rowIdx,
        columnIdx: widget.columnIdx,
        rowId: widget.rowId,
        columnId: widget.columnId,
      );
    } else if (columnType is AppTableColumnTypeDate) {
      cell = DateCell(
        state: isEdit ? CellStateEnum.edit : CellStateEnum.normal,
        column: widget.column,
        rowData: widget.rowData,
        rowIdx: widget.rowIdx,
        columnIdx: widget.columnIdx,
        rowId: widget.rowId,
        columnId: widget.columnId,
      );
    } else if (columnType is AppTableColumnTypeSingleSelect) {
      cell = SelectCell(
        state: isEdit ? CellStateEnum.edit : CellStateEnum.normal,
        column: widget.column,
        rowData: widget.rowData,
        rowIdx: widget.rowIdx,
        columnIdx: widget.columnIdx,
        rowId: widget.rowId,
        columnId: widget.columnId,
      );
    } else if (columnType is AppTableColumnTypeTelephone) {
      cell = TelephoneCell(
        state: isEdit ? CellStateEnum.edit : CellStateEnum.normal,
        column: widget.column,
        rowData: widget.rowData,
        rowIdx: widget.rowIdx,
        columnIdx: widget.columnIdx,
        rowId: widget.rowId,
        columnId: widget.columnId,
      );
    } else if (columnType is AppTableColumnTypeHyperlink) {
      cell = HyperlinkCell(
        state: isEdit ? CellStateEnum.edit : CellStateEnum.normal,
        column: widget.column,
        rowData: widget.rowData,
        rowIdx: widget.rowIdx,
        columnIdx: widget.columnIdx,
        rowId: widget.rowId,
        columnId: widget.columnId,
      );
    } else if (columnType is AppTableColumnTypeText) {
      cell = TextCell(
        state: isEdit ? CellStateEnum.edit : CellStateEnum.normal,
        column: widget.column,
        rowData: widget.rowData,
        rowIdx: widget.rowIdx,
        columnIdx: widget.columnIdx,
        rowId: widget.rowId,
        columnId: widget.columnId,
      );
    } else {
      print('占位');
      cell = Placeholder();
    }

    return cell;
  }
}
