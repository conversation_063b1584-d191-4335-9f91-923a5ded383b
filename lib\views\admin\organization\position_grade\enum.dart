import 'package:octasync_client/imports.dart';

/// 职位类型枚举
class PositionGradeEnum {
  /// 私有构造函数，防止实例化
  PositionGradeEnum._();

  /// 职位类型选项列表
  static const List<SelectOption<int>> positionOptions = [
    SelectOption(value: 1, label: '管理岗位'),
    SelectOption(value: 2, label: '软/硬件研发'),
    SelectOption(value: 3, label: '营销类'),
    SelectOption(value: 4, label: '行政后勤'),
    SelectOption(value: 5, label: '人力资源'),
    SelectOption(value: 6, label: '财务管理'),
    SelectOption(value: 7, label: '市场调研管理'),
    SelectOption(value: 8, label: '质量管理'),
    SelectOption(value: 9, label: '供应链'),
    SelectOption(value: 10, label: '安装售后'),
  ];
}
