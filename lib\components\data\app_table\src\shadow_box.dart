import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class ShadowBox extends StatelessWidget {
  const ShadowBox({
    super.key,
    this.borderRadius = 4,
    this.color = const Color.fromRGBO(192, 184, 184, 1),
    this.offset = const Offset(0, 0),
    this.blurRadius = 8,
    required this.child,
  });

  /// 阴影容器圆角
  final double borderRadius;

  /// 阴影颜色
  final Color color;

  /// 阴影偏移量
  final Offset offset;

  /// 模糊半径
  final double blurRadius;

  /// 扩散半径
  final double spreadRadius = 0;

  /// 子组件
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        // color: Colors.white,
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: color, //  const Color.fromRGBO(192, 184, 184, 1), // 阴影颜色
            offset: offset, // 偏移量 (x, y)
            blurRadius: blurRadius, // 模糊半径
            spreadRadius: spreadRadius, // 扩散半径
          ),
        ],
      ),
      child: child,
    );
  }
}
