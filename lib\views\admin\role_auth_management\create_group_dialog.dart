import 'package:flutter/material.dart';
import 'package:octasync_client/api/positions.dart';
import 'package:octasync_client/api/role_auth_management.dart';
import 'package:octasync_client/components/data/app_table/app_table.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/role_mgmt/role.dart';
import 'package:octasync_client/views/admin/role_auth_management/dialog_type_enum.dart';

/// 角色弹窗组件
class CreateGroupDialog extends StatefulWidget {
  final Widget? child;
  final void Function()? onSuccess; // 提交成功回调

  const CreateGroupDialog({super.key, this.child, this.onSuccess});

  @override
  State<CreateGroupDialog> createState() => CreateGroupDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class CreateGroupDialogState extends State<CreateGroupDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;

  /// 当前弹窗类型
  DialogTypeEmun _dialogType = DialogTypeEmun.create;

  GroupConfig currentGroup = GroupConfig();

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  /// 重置数据
  void resetFormData([StateSetter? setDialogState]) {
    currentGroup = GroupConfig();
    _nameController.text = '';
    if (setDialogState != null) {
      setDialogState(() {
        // isAddNext = false;
      });
    }
  }

  // /// 通过详情接口获取并填充编辑数据
  Future<void> fillEditData(GroupConfig group) async {
    // try {
    //   // // 调用详情接口获取完整数据
    //   // final response = await PositionsApi.getDetails({'Id': id});

    //   // if (response != null) {
    //   //   // 将响应数据转换为 currentGroup
    //   //   currentGroup = Role.fromJson(response);
    //   // }
    //   // 填充表单数据
    // } catch (e) {
    //   ToastManager.error('获取详情失败');
    // }
    currentGroup = group;
    _nameController.text = currentGroup.name ?? '';
  }

  /// 提交数据 - 支持创建和编辑
  Future<void> submitRequest(BuildContext context, StateSetter setDialogState) async {
    // 使用 Form 的校验功能
    if (!_formKey.currentState!.validate()) {
      ToastManager.error('请填写完整信息');
      return;
    }

    setDialogState(() {
      btnLoading = true;
    });

    try {
      const apiMap = {
        DialogTypeEmun.create: RoleAuthManagementApi.add,
        DialogTypeEmun.edit: RoleAuthManagementApi.add,
      };

      if (_dialogType == DialogTypeEmun.create) {
        currentGroup.id = '00000000-0000-0000-0000-000000000000';
      }

      print('提交对象');
      print(currentGroup.toJson());

      await apiMap[_dialogType]!(currentGroup.toJson());

      ToastManager.success('操作成功');

      setDialogState(() {
        btnLoading = false;
      });

      // 只有创建模式才重置表单（编辑模式直接关闭）
      if (_dialogType == DialogTypeEmun.create) {
        resetFormData(setDialogState);
      }

      widget.onSuccess?.call();

      // 创建模式且不继续添加，或编辑模式，则关闭弹窗
      if (mounted && (_dialogType == DialogTypeEmun.edit || !isAddNext)) {
        Navigator.of(context).pop();
      }
    } catch (err) {
      setDialogState(() {
        btnLoading = true;
      });
    }
  }

  /// 打开角色弹窗 - 支持创建和编辑
  Future<void> showCreateGroupDialog(
    BuildContext context, {
    DialogTypeEmun type = DialogTypeEmun.create,
    // String? id,
    GroupConfig? group,
  }) async {
    _dialogType = type;

    // 重置表单数据
    resetFormData();

    // 如果是编辑模式，先加载数据再显示弹窗
    if (type == DialogTypeEmun.edit && group != null) {
      // currentGroup.id = id;
      // currentGroup = role;
      fillEditData(group);
    }

    // 检查组件是否仍然挂载
    if (!mounted) return;

    double labelWidth = 80;

    Widget buildNameInput() {
      return AppInput(
        label: "名称",
        required: true,
        labelWidth: labelWidth,
        labelPosition: LabelPosition.left,
        hintText: "名称",
        size: InputSize.medium,
        controller: _nameController,
        maxLength: 30,
        validator: (value) {
          // 优先使用 controller 的值进行验证，确保编辑模式下能正确获取值
          final currentValue = value ?? _nameController.text;
          if (currentValue.isEmpty) {
            return '请输入名称';
          }
          return null;
        },
        onChanged: (value) {
          currentGroup.name = value;
        },
      );
    }

    AppDialog.show(
      width: 400,
      height: 300,
      context: context,
      title: _dialogType == DialogTypeEmun.create ? '添加角色组' : '编辑角色组',
      // isDrawer: true,
      // slideDirection: SlideDirection.right,
      showFooter: false,
      barrierDismissible: true,
      padding: EdgeInsetsGeometry.zero,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          // // 如果是编辑模式且有ID，异步加载数据
          // if (type == DialogTypeEmun.edit && id != null) {
          //   fillEditData(id);
          // }

          return Column(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 10,
                      children: [buildNameInput()],
                    ),
                  ),
                ),
              ),
              Divider(),
              Padding(
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // 只有创建模式才显示"继续新建下一条"选项
                    // if (_dialogType == DialogTypeEmun.create) ...[
                    //   Checkbox(
                    //     value: isAddNext,
                    //     onChanged: (value) {
                    //       setDialogState(() {
                    //         isAddNext = !isAddNext;
                    //       });
                    //     },
                    //   ),
                    //   Text('继续新建下一条'),
                    //   const SizedBox(width: 10),
                    // ],
                    AppButton(
                      text: '取消',
                      type: ButtonType.default_,
                      onPressed: () => context.pop(),
                    ),
                    const SizedBox(width: 10),
                    AppButton(
                      text: '确定',
                      type: ButtonType.primary,
                      loading: btnLoading,
                      onPressed: () => submitRequest(context, setDialogState),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child ?? SizedBox();
  }
}
