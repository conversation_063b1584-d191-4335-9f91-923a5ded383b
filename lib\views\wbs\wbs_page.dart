import 'package:flutter/material.dart';
import 'package:jyt_wbs_package/jyt_wbs.dart';
import 'package:octasync_client/main.dart';

class WbsPage extends StatelessWidget {
  const WbsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return JytWbs(
      baseUrl: 'http://**************:7001/',
      basePort: ':7001/',
      resourcesPort: ':8082/',
      token:
          'ZIxgPnsAl9gerlM8IFMQSWbhv5QU9xR/0yC4RjJaPWjLcUu1kk2Ipo52f27uQihCD+4YnHsiFAB2V0IkYqmWSGco4YB8uggIPsh3+aWghSlTFwmjrjWYhh4Li0GYhKGkLD9dOtVdsr9aa5x2ZeB5cafetyllO90Ibl3mn0c2tmT+3WYepEvvt4J34Y+hSW4SSamesCihoSnt9X4m8yXplRgSwlTE+VVaEdGdyZKgdDY=',
      userInfo: <String, dynamic>{
        'EmployeeId': '6dae65dd-64e2-4f08-a3ed-10467d6ae8da',
        'Name': '姜太一',
        'Avatar':
            'Files/Images/202410/c0ac82eb901daad4384daae33123a3e4/eaf81a4c510fd9f9a76cb5762c2dd42a2834a4b2.jpg',
        'Number': '000887',
      },
      navigatorKey: navigatorKey,
    );
  }
}
