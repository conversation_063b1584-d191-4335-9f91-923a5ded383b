import 'package:flutter/material.dart';
import 'package:octasync_client/models/route_item.dart';
import 'package:octasync_client/layout/desktop_main_shell.dart';
import 'package:octasync_client/layout/web_main_shell.dart';
import 'package:octasync_client/layout/window_bar_wrapper.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 基础路由提供器
///
/// 提供路由系统的基础功能，包括：
/// 1. 维护路由列表和路由项配置
/// 2. 为不同类型的路由提供不同的包装器
/// 3. 处理路由跳转、重定向和守卫逻辑
abstract class BaseRouterProvider with ChangeNotifier {
  /// 这是一个抽象的 getter 方法，在子类中实现。
  /// 系统会根据当前平台环境动态选择使用哪个子类的实现：
  /// - 在桌面环境下，使用 DesktopRouterProvider 的实现
  /// - 在 Web 环境下，使用 WebRouterProvider 的实现
  List<RouteItem> get allRouteList;

  /// 获取路由
  List<RouteBase> get getRoutes => AppUtil.isDesktop ? getDesktopRoutes() : getWebRoutes();

  /// 获取桌面端路由
  List<RouteBase> getDesktopRoutes() {
    return [
      GoRoute(path: '/', redirect: (context, state) => '/message'),
      // 显示菜单路由
      ShellRoute(
        builder: (context, state, child) => DesktopMainShell(child: child),
        routes: getMenuRoutes.map((routeItem) => _createGoRoute(routeItem)).toList(),
      ),
      // 不显示菜单但显示WindowBar的路由(如登录页等)
      if (getNoMenuRoutes.isNotEmpty)
        ShellRoute(
          builder: (context, state, child) => WindowBarWrapper(child: child),
          routes: getNoMenuRoutes.map((routeItem) => _createGoRoute(routeItem)).toList(),
        ),
    ];
  }

  /// 获取Web端路由
  List<RouteBase> getWebRoutes() {
    return [
      // GoRoute(path: '/', redirect: (context, state) => '/enterpriseManagement/enterpriseInfo'),
      GoRoute(path: '/', redirect: (context, state) => '/organization/memberDepartment'),
      // 显示菜单路由
      ShellRoute(
        builder: (context, state, child) => WebMainShell(child: child),
        routes: getMenuRoutes.map((routeItem) => _createGoRoute(routeItem)).toList(),
      ),
      // 不显示菜单(如登录页等)
      if (getNoMenuRoutes.isNotEmpty)
        ShellRoute(
          builder: (context, state, child) => child,
          routes: getNoMenuRoutes.map((routeItem) => _createGoRoute(routeItem)).toList(),
        ),
    ];
  }

  /// 获取菜单路由
  List<RouteItem> get getMenuRoutes =>
      allRouteList.where((routeItem) => routeItem.showMenuLayout).toList();

  /// 获取不显示菜单路由(如登录页等)
  List<RouteItem> get getNoMenuRoutes =>
      allRouteList.where((routeItem) => !routeItem.showMenuLayout).toList();

  // 递归创建路由
  GoRoute _createGoRoute(RouteItem routeItem) {
    return GoRoute(
      path: routeItem.path,
      name: routeItem.name,
      builder: routeItem.builder,
      onExit: routeItem.onExit,
      parentNavigatorKey: routeItem.parentNavigatorKey,
      redirect: routeItem.redirect,
      routes: routeItem.routes.map((child) => _createGoRoute(child as RouteItem)).toList(),
    );
  }
}
