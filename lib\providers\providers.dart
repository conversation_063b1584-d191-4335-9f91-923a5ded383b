import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'theme_provider.dart';
import 'package:octasync_client/providers/router/desktop_router_provider.dart';
import 'package:octasync_client/providers/router/web_router_provider.dart';
import 'app_provider.dart';
import 'user_provider.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class Providers extends StatelessWidget {
  final Widget child;

  const Providers({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider(context)),
        ChangeNotifierProvider(
          create: (_) => AppUtil.isDesktop ? DesktopRouterProvider() : WebRouterProvider(),
        ),
        ChangeNotifierProvider(create: (_) => AppProvider()),
        ChangeNotifierProvider(create: (_) => UserProvider()),
      ],
      child: child,
    );
  }
}
