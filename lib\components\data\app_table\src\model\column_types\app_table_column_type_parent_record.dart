import 'package:octasync_client/components/data/app_table/src/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/src/model/column_types/app_table_column_type_text.dart';
import 'package:json_annotation/json_annotation.dart';

part 'app_table_column_type_parent_record.g.dart';

@JsonSerializable()
class AppTableColumnTypeParentRecord extends AppTableColumnTypeText {
  AppTableColumnTypeParentRecord({
    super.defaultValue,
    super.columnDesc,
    super.isSaveRequired,
    super.isSubmitRequired,
    super.typeCode = ColumnTypeEnum.parentRecord,
  }) : super();

  // // 可选：添加额外属性或方法
  // String get protocol => Uri.tryParse(defaultValue)?.scheme ?? '';

  factory AppTableColumnTypeParentRecord.fromJson(Map<String, dynamic> json) =>
      _$AppTableColumnTypeParentRecordFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AppTableColumnTypeParentRecordToJson(this);
}
