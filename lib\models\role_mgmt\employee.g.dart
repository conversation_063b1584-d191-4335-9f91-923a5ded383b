// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Employee _$EmployeeFromJson(Map<String, dynamic> json) => Employee(
  employeeId: json['EmployeeId'] as String?,
  name: json['Name'] as String? ?? '',
  avatar: json['Avatar'] as String? ?? '',
  number: json['Number'] as String? ?? '',
  sexEnum: (json['Sexenum'] as num?)?.toInt(),
  phoneNumber: json['PhoneNumber'] as String? ?? '',
  email: json['Email'] as String? ?? '',
  avatarId: json['AvatarId'] as String?,
  employeeTypeEnum: (json['EmployeeTypeenum'] as num?)?.toInt(),
  fixedPhoneNumber: json['FixedPhoneNumber'] as String? ?? '',
  wxNumber: json['WxNumber'] as String? ?? '',
  superiorEmployeeId: json['SuperiorEmployeeId'] as String?,
  stateEnum: (json['Stateenum'] as num?)?.toInt(),
  dateLeft:
      json['DateLeft'] == null
          ? null
          : DateTime.parse(json['DateLeft'] as String),
  handover: json['Handover'] as bool?,
);

Map<String, dynamic> _$EmployeeToJson(Employee instance) => <String, dynamic>{
  'EmployeeId': instance.employeeId,
  'Name': instance.name,
  'AvatarId': instance.avatarId,
  'Avatar': instance.avatar,
  'Number': instance.number,
  'Sexenum': instance.sexEnum,
  'PhoneNumber': instance.phoneNumber,
  'Email': instance.email,
  'EmployeeTypeenum': instance.employeeTypeEnum,
  'FixedPhoneNumber': instance.fixedPhoneNumber,
  'WxNumber': instance.wxNumber,
  'SuperiorEmployeeId': instance.superiorEmployeeId,
  'Stateenum': instance.stateEnum,
  'DateLeft': instance.dateLeft?.toIso8601String(),
  'Handover': instance.handover,
};
