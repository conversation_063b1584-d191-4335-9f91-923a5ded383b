/// WBS配置类型枚举
enum WBSConfigTypeEnum {
  normalComponent(1, '普通组件'),
  planningPackage(2, '规划包'),
  workPackage(3, '工作包');

  final int value;
  final String label;

  const WBSConfigTypeEnum(this.value, this.label);

  /// 根据值查找对应的枚举
  static WBSConfigTypeEnum? fromValue(int value) {
    for (var type in WBSConfigTypeEnum.values) {
      if (type.value == value) {
        return type;
      }
    }
    return null;
  }

  /// 直接通过数值获取对应的中文描述
  /// 如果未找到匹配的枚举值，返回null
  static String? getLabel(int value) {
    final type = fromValue(value);
    return type?.label;
  }
}
