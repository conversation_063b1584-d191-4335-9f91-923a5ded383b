import 'package:octasync_client/components/data/app_table/src/model/general_option.dart';

class AppTableCommon {
  /// 日期列支持的日期格式
  static List<GeneralOption> dateFormats = [
    GeneralOption(id: 1, text: 'yyyy/MM/dd'),
    GeneralOption(id: 2, text: 'yyyy-MM-dd'),
    GeneralOption(id: 3, text: 'yyyy年MM月dd日'),
    GeneralOption(id: 4, text: 'yyyy年MM月'),
    GeneralOption(id: 5, text: 'MM/dd'),
  ];

  /// 根据dateType类型获取日期格式，如果没有找到，返回第一个日期格式
  static GeneralOption getDateFormatOption(int dateType) {
    return dateFormats.firstWhere(
      (element) => element.id == dateType,
      orElse: () => dateFormats[0],
    );
  }

  static List<GeneralOption<int>> currencyTypes = [
    GeneralOption(id: 1, text: '人民币CNY', symbol: '￥'),
    GeneralOption(id: 2, text: '美元USD', symbol: '\$'),
    GeneralOption(id: 3, text: '欧元EUR', symbol: '€'),
    GeneralOption(id: 4, text: '英镑GBP', symbol: '£'),
    GeneralOption(id: 5, text: '日元JPY', symbol: '￥'),
  ];

  /// 根据dateType类型获取日期格式，如果没有找到，返回第一个日期格式
  static GeneralOption<int>? getCurrencyType(int currentType) {
    var result = currencyTypes.where((ele) => ele.id == currentType);
    return result.isNotEmpty ? result.first : null;
  }
}
