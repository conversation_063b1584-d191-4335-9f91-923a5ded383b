import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

class RouteItem extends GoRoute {
  /// 菜单标题
  final String? title;

  /// 菜单icon
  final IconData? icon;

  /// 菜单排序
  final int? order;

  /// 如果设置为false，项目将不会显示在侧边栏中（默认为true）
  /// 只判断第一层不判断子路由,如果为false子路由也不会显示在侧边栏中
  final bool showMenuLayout;

  RouteItem({
    required super.path,
    super.builder,
    super.name,
    super.parentNavigatorKey,
    super.redirect,
    super.onExit,
    this.showMenuLayout = true,
    this.title,
    this.icon,
    this.order,
    List<RouteItem> routes = const [],
  }) : super(routes: routes);
}
