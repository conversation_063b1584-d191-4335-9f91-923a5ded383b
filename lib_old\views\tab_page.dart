import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:octasync_client/controllers/tab_controller.dart';

class TabPage extends StatelessWidget {
  final TabsController controller = Get.put(TabsController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('GetX Tab Demo'),
        bottom: TabBar(
          // 直接使用 TabBar（不要用 Obx 包裹）
          controller: controller.tabController, // 添加 TabController
          onTap: controller.changeTab,
          tabs: [
            _buildTab('Tab 1', 0),
            _buildTab('Tab 2', 1),
          ],
          indicator: const UnderlineTabIndicator(
            borderSide: BorderSide(color: Colors.blue, width: 2.0),
            insets: EdgeInsets.symmetric(horizontal: 16.0),
          ),
        ),
      ),
      body: PageView(
        controller: controller.pageController,
        onPageChanged: (index) => controller.currentIndex.value = index,
        children: const [
          Center(child: Text('Content 1')),
          Center(child: Text('Content 2')),
        ],
      ),
    );
  }

  Widget _buildTab(String text, int index) {
    return Tab(
      child: Obx(
        // 将 Obx 放在 Tab 内部
        () => Text(
          text,
          style: TextStyle(
            color: controller.currentIndex.value == index
                ? Colors.blue
                : Colors.grey,
          ),
        ),
      ),
    );
  }
}
