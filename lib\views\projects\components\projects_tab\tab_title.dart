import 'package:flutter/material.dart';
import 'package:octasync_client/views/projects/providers/sidebar_provider.dart';
import 'package:provider/provider.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class TabTitle extends StatelessWidget {
  const TabTitle({super.key});

  @override
  Widget build(BuildContext context) {
    var borderColor = Variables.commonBorderColor(context);
    return Selector<SideBarProvider, ({SideBarItem item, bool isShow})>(
      selector:
          (context, provider) => (item: provider.currentCheckedItem, isShow: provider.showSideBar),
      builder: (context, data, child) {
        final myProvider = context.read<SideBarProvider>();

        return Row(
          children: [
            SizedBox(width: 10),
            SizedBox(
              height: 40,
              child: Padding(
                padding: EdgeInsets.only(top: 2),
                child: GestureDetector(
                  onTap: () => {myProvider.toggleSideBar()},
                  child: AppCustomTooltip(
                    placement: TooltipPlacement.leftCenter,
                    content: data.isShow ? '隐藏' : '显示',
                    child: Center(
                      child: Icon(
                        IconFont.xianxing_caidanzhedie,
                        size: AppIconSize.small,
                        color: context.icon300,
                      ),
                    ),
                  ),

                  // AppTooltip(
                  //   message: data.isShow ? '隐藏' : '显示',
                  //   child: SvgPicture.asset(
                  //     'assets/icons/xianxing_caidanzhedie.svg',
                  //     width: AppIconSize.small,
                  //     height: AppIconSize.small,
                  //     // colorFilter: ColorFilter.mode(data.item.color, BlendMode.srcIn),
                  //   ),
                  // ),
                ),
              ),
            ),
            SizedBox(width: 10),
            SizedBox(
              height: 40,
              child: Padding(
                padding: EdgeInsets.only(top: 2),
                child: Icon(data.item.icon, size: AppIconSize.small, color: data.item.color),
              ),
            ),
            SizedBox(width: Variables.iconTextSpace),
            Text(data.item.title, style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
            SizedBox(width: 10),
            VerticalDivider(width: 1, color: borderColor),
          ],
        );
      },
    );
  }
}
