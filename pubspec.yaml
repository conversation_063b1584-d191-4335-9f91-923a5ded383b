name: octasync_client
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  go_router: ^15.1.3
  provider: ^6.1.4
  shared_preferences: ^2.5.2
  dio: ^5.8.0+1
  build_runner: ^2.4.15
  json_annotation: ^4.9.0
  window_manager: ^0.5.0
  carousel_slider: ^5.0.0
  crypto: ^3.0.6
  cached_network_image: ^3.3.1
  flutter_cache_manager: ^3.3.1
  tray_manager: ^0.5.0
  equatable: ^2.0.7
  file_picker: ^10.1.9
  crop_image: ^1.0.16
  cupertino_icons: ^1.0.8

  linked_scroll_controller: ^0.2.0
  uuid: ^4.5.1
  intl: any #格式化包
  collection: ^1.19.1
  decimal: ^3.2.4
  url_launcher: ^6.2.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  json_serializable: ^6.9.4
  jyt_wbs_package:
    path: ../jyt_wbs_package
    #  git:
    #   url: http://git1.jiayuntong.com/platform-dev/jyt_wbs_package.git
    #   ref: master
  jyt_components_package:
    path: ../jyt_components_package
    #  git:
    #   url: http://git1.jiayuntong.com/platform-dev/jyt_components_package.git
    #   ref: master

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/icons/
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: SourceHanSans
      fonts:
        - asset: assets/fonts/SourceHanSansCN-Regular.otf
          weight: 400
        - asset: assets/fonts/SourceHanSansCN-Bold.otf
          weight: 700
    - family: IconFont
      fonts:
        - asset: assets/icons/iconfont.ttf
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package