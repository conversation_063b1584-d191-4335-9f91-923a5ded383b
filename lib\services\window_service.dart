import 'package:window_manager/window_manager.dart';
import 'package:octasync_client/services/tray_service.dart';

/// 窗口服务类，用于管理窗口相关功能
class WindowService {
  WindowService._internal();
  static final WindowService _instance = WindowService._internal();
  factory WindowService() => _instance;

  /// 是否已初始化
  bool _isInitialized = false;

  /// 是否允许关闭应用(直接关闭应用而不是收起到托盘)
  bool _allowClose = false;

  /// 初始化窗口服务
  Future<void> init() async {
    if (_isInitialized) return;

    await windowManager.ensureInitialized();

    // 设置窗口关闭时的回调
    windowManager.setPreventClose(true);
    windowManager.addListener(_WindowListener());

    _isInitialized = true;
  }

  /// 设置是否允许关闭应用
  void setAllowClose(bool allow) {
    _allowClose = allow;
  }

  /// 获取是否允许关闭应用
  bool get allowClose => _allowClose;

  /// 退出应用
  Future<void> exitApp() async {
    // 清理托盘资源
    await TrayService().destroy();
    // 销毁窗口
    await windowManager.destroy();
  }
}

/// 窗口事件监听器
class _WindowListener with WindowListener {
  @override
  void onWindowClose() async {
    // 获取窗口服务实例
    final windowService = WindowService();

    if (windowService.allowClose) {
      // 如果允许关闭，则退出应用
      await windowService.exitApp();
    } else {
      // 否则隐藏窗口到托盘
      bool isVisible = await windowManager.isVisible();
      if (isVisible) {
        await windowManager.hide();
      }
    }
  }
}
