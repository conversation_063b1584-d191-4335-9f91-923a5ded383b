// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'general_option.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

GeneralOption<T> _$GeneralOptionFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => GeneralOption<T>(
  id: fromJsonT(json['id']),
  text: json['text'] as String,
  symbol: json['symbol'] as String?,
);

Map<String, dynamic> _$GeneralOptionToJson<T>(
  GeneralOption<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'id': toJsonT(instance.id),
  'text': instance.text,
  'symbol': instance.symbol,
};
