import 'package:octasync_client/utils/http_service.dart';

final _http = HttpService();

const controller = '/Business/EnterpriseConfig/';

/// 企业信息配置
class EnterpriseConfigApi {
  static Future<dynamic> getDetail(data) {
    return _http.post('${controller}GetDetail', data: data);
  }

  static Future<dynamic> addAndEdit(data) {
    return _http.post('${controller}AddAndEdit', data: data);
  }

  static Future<dynamic> getLoginPageInfo(data) {
    return _http.post('${controller}GetLoginPage', data: data);
  }
}
