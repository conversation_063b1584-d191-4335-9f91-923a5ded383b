import 'package:flutter/material.dart';
import 'package:octasync_client/providers/app_provider.dart';
import 'package:window_manager/window_manager.dart';
import 'package:octasync_client/layout/window_bar/window_buttons.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/providers/theme_provider.dart';
import 'package:octasync_client/layout/window_bar/window_avatar.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class WindowBar extends StatelessWidget {
  const WindowBar({super.key});

  @override
  Widget build(BuildContext context) {
    final windowBarContentVisible = context.select(
      (AppProvider model) => model.windowBarContentVisible,
    );

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onPanStart: (details) => windowManager.startDragging(),
      child: Container(
        height: 40,
        color: windowBarContentVisible ? context.windowBarColor : Colors.transparent,
        child: Row(
          children: [
            if (AppUtil.isMacOS) const WindowButtons(),
            if (windowBarContentVisible)
              Expanded(child: WindowBarContent())
            else
              buildDoubleClickArea(),
            if (!AppUtil.isMacOS) const WindowButtons(),
          ],
        ),
      ),
    );
  }
}

/// 窗口栏主要内容
class WindowBarContent extends StatelessWidget {
  const WindowBarContent({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(width: 18),
        if (!AppUtil.isMacOS) WindowAvatar(),

        buildDoubleClickArea(),
        IconButton(
          icon: const Icon(Icons.brightness_6),
          color: context.icon300,
          onPressed: context.read<ThemeProvider>().toggleTheme,
        ),
      ],
    );
  }
}

/// 创建一个可双击最大化的透明区域
Widget buildDoubleClickArea() {
  /// 双击窗口最大化处理函数
  Future<void> handleWindowMaximize() async {
    bool isMaximized = await windowManager.isMaximized();
    if (isMaximized) {
      await windowManager.unmaximize();
    } else {
      await windowManager.maximize();
    }
  }

  return Expanded(
    child: GestureDetector(
      onDoubleTap: handleWindowMaximize,
      child: Container(color: Colors.transparent),
    ),
  );
}
