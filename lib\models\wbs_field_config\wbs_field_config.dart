import 'package:json_annotation/json_annotation.dart';

part 'wbs_field_config.g.dart';

@JsonSerializable()
class WbsFieldConfig {
  @Json<PERSON>ey(name: 'Id')
  String? id;
  @J<PERSON><PERSON>ey(name: 'WBSConfigTypeenum')
  int? wbsConfigTypeenum;
  @J<PERSON><PERSON><PERSON>(name: 'OrderIndex', defaultValue: 0)
  int orderIndex;
  @<PERSON>son<PERSON>ey(name: 'DisplayName', defaultValue: '')
  String displayName;
  @<PERSON>son<PERSON>ey(name: 'DataTypeenum')
  dynamic dataTypeenum;
  @Json<PERSON>ey(name: 'Placeholder')
  dynamic placeholder;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'OptionValues')
  dynamic optionValues;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Format')
  dynamic format;
  @Json<PERSON>ey(name: 'Enabled', defaultValue: false)
  bool enabled;
  @<PERSON>son<PERSON>ey(name: 'Visible', defaultValue: false)
  bool visible;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'RequiredCreate', defaultValue: false)
  bool requiredCreate;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'RequiredSubmit', defaultValue: false)
  bool requiredSubmit;
  @<PERSON>son<PERSON><PERSON>(name: 'Editable', defaultValue: false)
  bool editable;
  @Json<PERSON>ey(name: 'DisabledField')
  dynamic disabledField;
  @JsonKey(name: 'FieldSourceenum')
  int? fieldSourceenum;

  WbsFieldConfig({
    this.id,
    this.wbsConfigTypeenum,
    this.orderIndex = 0,
    this.displayName = '',
    this.dataTypeenum,
    this.placeholder,
    this.optionValues,
    this.format,
    this.enabled = false,
    this.visible = false,
    this.requiredCreate = false,
    this.requiredSubmit = false,
    this.editable = false,
    this.disabledField,
    this.fieldSourceenum,
  });

  factory WbsFieldConfig.fromJson(Map<String, dynamic> json) {
    return _$WbsFieldConfigFromJson(json);
  }

  Map<String, dynamic> toJson() => _$WbsFieldConfigToJson(this);
}
