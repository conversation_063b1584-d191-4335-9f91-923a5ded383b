import 'package:octasync_client/views/wbs/wbs_page.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/models/route_item.dart';
import 'package:octasync_client/providers/app_provider.dart';
import 'package:octasync_client/views/example/index.dart';
import 'package:octasync_client/views/message/message_page.dart';
import 'package:octasync_client/views/login/login_page.dart';
import 'package:octasync_client/views/collaboration/collaboration_page.dart';
import 'package:octasync_client/views/contacts/contacts_page.dart';
import 'package:octasync_client/views/workbench/workbench_page.dart';
import 'package:octasync_client/views/calendar/calendar_page.dart';
import 'package:octasync_client/views/projects/projects_index.dart';
import 'package:octasync_client/views/hr_admin/hr_admin_page.dart';
import 'base_router_provider.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 桌面端路由提供器
///
/// 实现桌面端特定的路由配置，包括：
/// 1. 桌面端特定的路由列表
/// 2. 桌面端特定的路由行为
///
/// 当应用在桌面环境（Windows 或 macOS）运行时，系统会使用这个路由提供者。
/// 它继承自 BaseRouterProvider，重写了 allRouteList 方法，提供桌面端特定的路由配置。

class DesktopRouterProvider extends BaseRouterProvider {
  /// 整个项目所有的路由
  final List<RouteItem> _allRouteList = [
    RouteItem(
      path: '/message',
      name: 'message',
      title: '消息',
      icon: IconFont.mianxing_xiaoxi,
      order: 10,
      builder: (context, state) => const MessagePage(),
    ),
    RouteItem(
      path: '/collaboration',
      name: 'collaboration',
      title: '协同',
      icon: IconFont.mianxing_xietong,
      order: 20,
      builder: (context, state) => const CollaborationPage(),
    ),
    RouteItem(
      path: '/contacts',
      name: 'contacts',
      title: '通讯录',
      icon: IconFont.mianxing_tongxunlu,
      order: 30,
      builder: (context, state) => const ContactsPage(),
    ),
    RouteItem(
      path: '/workbench',
      name: 'workbench',
      title: '工作台',
      icon: IconFont.mianxing_gongzuotai,
      order: 40,
      builder: (context, state) => const WorkbenchPage(),
    ),
    RouteItem(
      path: '/calendar',
      name: 'calendar',
      title: '日历',
      icon: IconFont.mianxing_rili,
      order: 50,
      builder: (context, state) => const CalendarPage(),
    ),
    RouteItem(
      path: '/projects',
      name: 'projects',
      title: '项目',
      icon: IconFont.mianxing_xiangmu,
      order: 60,
      builder: (context, state) => const ProjectsIndex(),
    ),
    RouteItem(
      path: '/hr_admin',
      name: 'hr_admin',
      title: '人事行政',
      icon: IconFont.mianxing_renshihangzheng,
      order: 70,
      builder: (context, state) => const HrAdminPage(),
    ),
    RouteItem(
      path: '/wbs',
      name: 'wbs',
      title: 'WBS',
      icon: IconFont.mianxing_renshihangzheng,
      order: 70,
      builder: (context, state) => const WbsPage(),
    ),
    RouteItem(
      path: '/login',
      name: 'login',
      showMenuLayout: false,
      builder: (context, state) => const LoginPage(),
      onExit: (context, state) {
        // 显示窗口栏
        Provider.of<AppProvider>(context, listen: false).setWindowBarContentVisible(true);
        return true;
      },
    ),
    //TODO:需要移除示例
    RouteItem(
      path: '/example',
      name: 'example',
      title: '示例',
      icon: IconFont.mianxing_renshihangzheng,
      order: 80,
      builder: (context, state) => const ExamplePage(),
    ),
  ];

  @override
  List<RouteItem> get allRouteList => _allRouteList;
}
