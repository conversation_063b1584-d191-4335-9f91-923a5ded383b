// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'employee_tree_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

EmployeeTreeModel _$EmployeeTreeModelFromJson(Map<String, dynamic> json) =>
    EmployeeTreeModel(
      id: json['Id'] as String?,
      avatar: json['Avatar'] as String?,
      name: json['Name'] as String? ?? '',
      parentId: json['ParentId'] as String?,
      parentName: json['ParentName'] as String? ?? '',
      type: (json['Type'] as num?)?.toInt() ?? 1,
      children:
          (json['Children'] as List<dynamic>?)
              ?.map(
                (e) => EmployeeTreeModel.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          [],
    );

Map<String, dynamic> _$EmployeeTreeModelToJson(EmployeeTreeModel instance) =>
    <String, dynamic>{
      if (instance.id case final value?) 'Id': value,
      if (instance.avatar case final value?) 'Avatar': value,
      'Name': instance.name,
      if (instance.parentId case final value?) 'ParentId': value,
      'ParentName': instance.parentName,
      'Type': instance.type,
      'Children': instance.children.map((e) => e.toJson()).toList(),
    };
