import 'package:json_annotation/json_annotation.dart';

part 'upload_response.g.dart';

@JsonSerializable()
class UploadResponse {
  @Json<PERSON><PERSON>(name: 'FilePath')
  String? filePath;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Path')
  String? path;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Poster')
  dynamic poster;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'UrlThum')
  dynamic urlThum;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'FileSize')
  int? fileSize;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'FileName')
  String? fileName;
  @Json<PERSON><PERSON>(name: 'FileMd5value')
  String? fileMd5value;
  @<PERSON>son<PERSON><PERSON>(name: 'FileId')
  String? fileId;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'ContentType')
  dynamic contentType;

  UploadResponse({
    this.filePath,
    this.path,
    this.poster,
    this.urlThum,
    this.fileSize,
    this.fileName,
    this.fileMd5value,
    this.fileId,
    this.contentType,
  });

  factory UploadResponse.from<PERSON>son(Map<String, dynamic> json) {
    return _$UploadResponseFromJson(json);
  }

  Map<String, dynamic> toJson() => _$UploadResponseToJson(this);
}
