import 'package:json_annotation/json_annotation.dart';

part 'department_model.g.dart';

@JsonSerializable()
class DepartmentModel {
  @Json<PERSON><PERSON>(name: 'ParentIdList', defaultValue: [])
  List<String> parentIdList;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'ParentList', defaultValue: [])
  List<DepartmentModel> parentList;
  @<PERSON>son<PERSON>ey(name: 'DepartmentHeadIdList', defaultValue: [])
  List<dynamic> departmentHeadIdList;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'DepartmentHeadList', defaultValue: [])
  List<dynamic> departmentHeadList;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Id')
  String? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'DepartmentName', defaultValue: '')
  String departmentName;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'ParentId')
  String? parentId;
  @Json<PERSON><PERSON>(name: 'ParentName')
  String? parentName;
  @<PERSON>son<PERSON>ey(name: 'SecondarySuperiorIds')
  String? secondarySuperiorIds;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'DepartmentHead')
  String? departmentHead;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'DepartmentHRBP')
  dynamic departmentHrbp;
  @JsonKey(name: 'Description', defaultValue: '')
  String description;

  DepartmentModel({
    this.parentIdList = const [],
    this.parentList = const [],
    this.departmentHeadIdList = const [],
    this.departmentHeadList = const [],
    this.id,
    this.departmentName = '',
    this.parentId,
    this.parentName = '',
    this.secondarySuperiorIds,
    this.departmentHead,
    this.departmentHrbp,
    this.description = '',
  });

  factory DepartmentModel.fromJson(Map<String, dynamic> json) {
    return _$DepartmentModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$DepartmentModelToJson(this);
}
