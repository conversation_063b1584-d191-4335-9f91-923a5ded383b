import 'package:octasync_client/components/data/app_table/src/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/src/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/src/enums/select_enum.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/src/model/general_option.dart';

import 'package:json_annotation/json_annotation.dart';

part 'app_table_column_type_single_select.g.dart';

/// 单选、多选公用组件
@JsonSerializable()
class AppTableColumnTypeSingleSelect
    with AppTableColumnTypeDefaultMixin
    implements AppTableColumnType {
  dynamic _defaultValue;
  @override
  dynamic get defaultValue => _defaultValue;
  @override
  set defaultValue(dynamic value) => _defaultValue = value;

  bool isSaveRequired = false;

  bool isSubmitRequired = false;

  String _columnDesc;
  @override
  String get columnDesc => _columnDesc;
  @override
  set columnDesc(String value) => _columnDesc = value;

  @JsonKey(fromJson: _optionsFromJson, toJson: _optionsToJson)
  List<GeneralOption<String>> options = [];

  /// 默认值类型（1：无；2：指定值）
  int defaultValueType = 1;

  // /// 是否为多选（false：单选；true：多选）
  // bool isMultiple;

  /// 子类型
  /// 如果是共用类型，以子类型为尊
  SelectEnum subType;

  String get formatStr {
    return '123';
  }

  @override
  ColumnTypeEnum typeCode = ColumnTypeEnum.singleSelect;

  AppTableColumnTypeSingleSelect({
    dynamic defaultValue = '',
    String columnDesc = '',
    // this.isMultiple = false,
    this.subType = SelectEnum.single,
  }) : _defaultValue = defaultValue,
       _columnDesc = columnDesc,
       typeCode =
           subType == SelectEnum.single
               ? ColumnTypeEnum.singleSelect
               : ColumnTypeEnum.multipleSelect;

  @override
  bool isValid(dynamic value) {
    return value is String || value is num;
  }

  @override
  int compare(dynamic a, dynamic b) {
    return AppTableGeneralHelper.compareWithNull(a, b, () => a.toString().compareTo(b.toString()));
  }

  @override
  dynamic markCompareValue(dynamic value) {
    return value.toString();
  }

  static List<GeneralOption<String>> _optionsFromJson(List<dynamic>? json) {
    if (json == null) return [];
    return json
        .map(
          (item) => GeneralOption<String>.fromJson(
            item as Map<String, dynamic>,
            (json) => json as String,
          ),
        )
        .toList();
  }

  static List<Map<String, dynamic>> _optionsToJson(List<GeneralOption<String>> options) {
    return options.map((option) => option.toJson((value) => value)).toList();
  }

  factory AppTableColumnTypeSingleSelect.fromJson(Map<String, dynamic> json) =>
      _$AppTableColumnTypeSingleSelectFromJson(json);

  @override
  Map<String, dynamic> toJson() => _$AppTableColumnTypeSingleSelectToJson(this);
}
