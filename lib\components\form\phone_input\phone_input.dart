import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:octasync_client/components/form/phone_input/phone_input_enum.dart';
import 'package:octasync_client/imports.dart';

class PhoneInput extends StatefulWidget {
  final void Function(String? areaCode, String? phoneNumber)? onChange;

  /// 外部区号控制器（可选）
  final SelectController<String>? areaCodeController;

  /// 外部电话号码控制器（可选）
  final TextEditingController? phoneController;

  const PhoneInput({super.key, this.onChange, this.areaCodeController, this.phoneController});

  @override
  State<PhoneInput> createState() => _PhoneInputState();
}

class _PhoneInputState extends State<PhoneInput> {
  late final SelectController<String> _areaCodeController;
  late final TextEditingController _phoneController;

  @override
  void initState() {
    super.initState();

    // 使用外部控制器或创建内部控制器
    _areaCodeController = widget.areaCodeController ?? SelectController<String>();
    _phoneController = widget.phoneController ?? TextEditingController();
  }

  @override
  void dispose() {
    // 只有在使用内部控制器时才需要释放资源
    if (widget.areaCodeController == null) {
      _areaCodeController.dispose();
    }
    if (widget.phoneController == null) {
      _phoneController.dispose();
    }
    super.dispose();
  }

  void change() {
    widget.onChange?.call(_areaCodeController.value, _phoneController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          width: 100,
          child: AppSelect<String>(
            placeholder: '区号',
            options: PhonmeInputEnum.areaCodeOptions,
            controller: _areaCodeController,
            onChanged: (value) => change(),
          ),
        ),
        SizedBox(width: 10),
        Expanded(
          child: AppInput(
            label: "",
            hintText: "手机号",
            size: InputSize.medium,
            controller: _phoneController,
            maxLength: 30,
            inputFormatters: [FilteringTextInputFormatter(RegExp(r'[0-9]'), allow: true)],
            onChanged: (value) => change(),
          ),
        ),
      ],
    );
  }
}
