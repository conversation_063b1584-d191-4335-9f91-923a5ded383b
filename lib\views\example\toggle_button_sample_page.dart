import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 切换按钮示例页面
///
/// 本页面展示了AppToggleButton组件的各种使用场景和配置选项
/// 包括：
/// 1. 不同尺寸的切换按钮
/// 2. 带禁用状态的按钮
/// 3. 不同图标组合
/// 4. 实际应用场景
class ToggleButtonSamplePage extends StatefulWidget {
  const ToggleButtonSamplePage({super.key});

  @override
  State<ToggleButtonSamplePage> createState() => _ToggleButtonSamplePageState();
}

class _ToggleButtonSamplePageState extends State<ToggleButtonSamplePage> {
  int _viewMode2 = 1;

  // 视图模式切换示例
  int _viewMode = 1;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('切换按钮示例')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 120),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('AppToggleButton 组件示例', style: Theme.of(context).textTheme.headlineMedium),
            const SizedBox(height: 8),
            const Text('本页面展示了AppToggleButton组件的各种用法和参数效果，帮助开发者了解如何正确使用此组件。'),
            _buildSizeExamples(),
            _buildDisabledExample(),
            _buildViewModeExample(),
          ],
        ),
      ),
    );
  }

  /// 不同尺寸示例
  Widget _buildSizeExamples() {
    return _buildExampleSection(
      '尺寸 (size)',
      'AppToggleButton提供四种尺寸：large, medium (默认), small, mini。每种尺寸都有其适用场景。',
      [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Large - 适用于主要操作区域'),
            const SizedBox(height: 8),
            AppToggleButton(
              value: _viewMode2,
              size: ToggleButtonSize.large,
              btns: [
                AppToggleButtonItem(
                  value: 1,
                  label: '看板视图',
                  iconData: IconFont.mianxing_shitu_kapian,
                ),
                AppToggleButtonItem(
                  value: 2,
                  label: '列表视图',
                  iconData: IconFont.mianxing_shitu_biaoge,
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _viewMode2 = value;
                });
              },
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Medium - 适用于常规操作区域'),
            const SizedBox(height: 8),
            AppToggleButton(
              value: _viewMode2,
              size: ToggleButtonSize.medium,
              btns: [
                AppToggleButtonItem(
                  value: 1,
                  label: '看板视图',
                  iconData: IconFont.mianxing_shitu_kapian,
                ),
                AppToggleButtonItem(
                  value: 2,
                  label: '列表视图',
                  iconData: IconFont.mianxing_shitu_biaoge,
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _viewMode2 = value;
                });
              },
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Small - 适用于次要操作区域'),
            const SizedBox(height: 8),
            AppToggleButton(
              value: _viewMode2,
              size: ToggleButtonSize.small,
              btns: [
                AppToggleButtonItem(
                  value: 1,
                  label: '看板视图',
                  iconData: IconFont.mianxing_shitu_kapian,
                ),
                AppToggleButtonItem(
                  value: 2,
                  label: '列表视图',
                  iconData: IconFont.mianxing_shitu_biaoge,
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _viewMode2 = value;
                });
              },
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Mini - 适用于紧凑空间'),
            const SizedBox(height: 8),
            AppToggleButton(
              value: _viewMode2,
              size: ToggleButtonSize.mini,
              btns: [
                AppToggleButtonItem(
                  value: 1,
                  label: '看板视图',
                  iconData: IconFont.mianxing_shitu_kapian,
                ),
                AppToggleButtonItem(
                  value: 2,
                  label: '列表视图',
                  iconData: IconFont.mianxing_shitu_biaoge,
                ),
              ],
              onChanged: (value) {
                setState(() {
                  _viewMode2 = value;
                });
              },
            ),
          ],
        ),
      ],
    );
  }

  /// 禁用状态示例
  Widget _buildDisabledExample() {
    return _buildExampleSection('禁用状态', '可以通过设置按钮项的disabled属性来禁用特定选项。禁用状态下，按钮会显示为灰色且无法点击。', [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('带禁用选项的按钮'),
          const SizedBox(height: 8),
          AppToggleButton(
            value: 1,
            btns: [
              AppToggleButtonItem(
                value: 1,
                label: '可用选项',
                iconData: IconFont.mianxing_shitu_kapian,
              ),
              AppToggleButtonItem(
                value: 2,
                label: '禁用选项',
                iconData: IconFont.mianxing_shitu_biaoge,
                disabled: true,
              ),
            ],
            onChanged: (value) {},
          ),
        ],
      ),
    ]);
  }

  /// 视图切换示例
  Widget _buildViewModeExample() {
    return _buildExampleSection('实际应用场景', '展示AppToggleButton在实际应用中的使用场景，如视图模式切换。', [
      Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('视图模式切换'),
          const SizedBox(height: 8),
          AppToggleButton(
            value: _viewMode,
            btns: [
              AppToggleButtonItem(
                value: 1,
                label: '看板视图',
                iconData: IconFont.mianxing_shitu_kapian,
              ),
              AppToggleButtonItem(
                value: 2,
                label: '列表视图',
                iconData: IconFont.mianxing_shitu_biaoge,
              ),
            ],
            onChanged: (value) {
              setState(() {
                _viewMode = value;
              });
            },
          ),
          const SizedBox(height: 8),
          Text('当前选中: ${_viewMode == 1 ? "看板视图" : "列表视图"}'),
        ],
      ),
    ]);
  }

  // 辅助方法：用于创建示例部分
  Widget _buildExampleSection(String title, String description, List<Widget> examples) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Text(title, style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 8),
        Text(description),
        const SizedBox(height: 16),
        Wrap(spacing: 16, runSpacing: 16, children: examples),
        const Divider(height: 40),
      ],
    );
  }
}
