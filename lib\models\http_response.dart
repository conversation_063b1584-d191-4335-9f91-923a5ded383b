import 'package:json_annotation/json_annotation.dart';

part 'http_response.g.dart';

@JsonSerializable()
class HttpResponse {
  @Json<PERSON>ey(name: 'MessageCode')
  int? messageCode;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'ErrorMessage')
  String? errorMessage;
  @Json<PERSON>ey(name: 'ExceptionMessage')
  String? exceptionMessage;
  @J<PERSON><PERSON><PERSON>(name: 'TMessageData')
  dynamic tMessageData;

  HttpResponse({this.messageCode, this.errorMessage, this.exceptionMessage, this.tMessageData});

  factory HttpResponse.fromJson(Map<String, dynamic> json) {
    return _$HttpResponseFromJson(json);
  }

  Map<String, dynamic> toJson() => _$HttpResponseToJson(this);
}
