import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class ButtonsExamplePage extends StatelessWidget {
  const ButtonsExamplePage({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('基础按钮'),
          _buildButtonsGroup([
            AppButton(text: '默认按钮', type: ButtonType.default_, onPressed: () {}),
            AppButton(text: '主要按钮', type: ButtonType.primary, onPressed: () {}),
            AppButton(text: '成功按钮', type: ButtonType.success, onPressed: () {}),
            AppButton(text: '信息按钮', type: ButtonType.info, onPressed: () {}),
            AppButton(text: '警告按钮', type: ButtonType.warning, onPressed: () {}),
            AppButton(text: '危险按钮', type: ButtonType.danger, onPressed: () {}),
          ]),

          _buildSectionTitle('圆角按钮'),
          _buildButtonsGroup([
            AppButton(text: '圆角按钮', type: ButtonType.default_, round: true, onPressed: () {}),
            AppButton(text: '主要按钮', type: ButtonType.primary, round: true, onPressed: () {}),
            AppButton(text: '成功按钮', type: ButtonType.success, round: true, onPressed: () {}),
            AppButton(text: '信息按钮', type: ButtonType.info, round: true, onPressed: () {}),
            AppButton(text: '警告按钮', type: ButtonType.warning, round: true, onPressed: () {}),
            AppButton(text: '危险按钮', type: ButtonType.danger, round: true, onPressed: () {}),
          ]),

          _buildSectionTitle('图标按钮'),
          _buildButtonsGroup([
            AppButton(
              iconData: Icons.edit,
              size: ButtonSize.large,
              type: ButtonType.primary,
              onPressed: () {},
            ),
            AppButton(
              iconData: Icons.edit,
              size: ButtonSize.medium,
              type: ButtonType.transparent,
              color: AppColors.primary,
              onPressed: () {},
            ),
            AppButton(
              type: ButtonType.transparent,
              size: ButtonSize.small,
              iconData: Icons.visibility_off,
              color: AppColors.icon200,
              onPressed: () {},
            ),

            AppButton(
              iconData: Icons.delete,
              size: ButtonSize.small,
              type: ButtonType.danger,
              onPressed: () {},
            ),
            AppButton(
              iconData: Icons.search,
              size: ButtonSize.small,
              type: ButtonType.default_,
              onPressed: () {},
            ),
            AppButton(
              text: '搜索',
              size: ButtonSize.large,
              iconData: Icons.search,
              type: ButtonType.primary,
              onPressed: () {},
            ),
            AppButton(
              text: '上传',
              size: ButtonSize.medium,
              iconData: Icons.upload,
              type: ButtonType.primary,
              onPressed: () {},
            ),
            AppButton(
              text: '下载',
              size: ButtonSize.small,
              iconData: Icons.download,
              type: ButtonType.primary,
              onPressed: () {},
            ),
          ]),

          _buildSectionTitle('按钮尺寸'),
          _buildButtonsGroup([
            AppButton(
              text: '大型按钮',
              type: ButtonType.primary,
              size: ButtonSize.large,
              onPressed: () {},
            ),
            AppButton(
              text: '中型按钮(默认)',
              type: ButtonType.primary,
              size: ButtonSize.medium,
              onPressed: () {},
            ),
            AppButton(
              text: '小型按钮',
              type: ButtonType.primary,
              size: ButtonSize.small,
              onPressed: () {},
            ),
            AppButton(
              text: '迷你按钮',
              type: ButtonType.primary,
              size: ButtonSize.mini,
              onPressed: () {},
            ),
          ]),

          _buildSectionTitle('禁用状态'),
          _buildButtonsGroup([
            AppButton(text: '默认按钮', type: ButtonType.default_, disabled: true, onPressed: () {}),
            AppButton(text: '主要按钮', type: ButtonType.primary, disabled: true, onPressed: () {}),
            AppButton(text: '成功按钮', type: ButtonType.success, disabled: true, onPressed: () {}),
            AppButton(text: '信息按钮', type: ButtonType.info, disabled: true, onPressed: () {}),
            AppButton(text: '警告按钮', type: ButtonType.warning, disabled: true, onPressed: () {}),
            AppButton(text: '危险按钮', type: ButtonType.danger, disabled: true, onPressed: () {}),
          ]),

          _buildSectionTitle('文字按钮'),
          _buildButtonsGroup([
            AppButton(text: '默认文字按钮', textOnly: true, onPressed: () {}),
            AppButton(text: '主要文字按钮', type: ButtonType.primary, textOnly: true, onPressed: () {}),
            AppButton(text: '成功文字按钮', type: ButtonType.success, textOnly: true, onPressed: () {}),
            AppButton(text: '警告文字按钮', type: ButtonType.warning, textOnly: true, onPressed: () {}),
            AppButton(text: '危险文字按钮', type: ButtonType.danger, textOnly: true, onPressed: () {}),
            AppButton(text: '禁用文字按钮', textOnly: true, disabled: true, onPressed: () {}),
          ]),

          _buildSectionTitle('加载状态'),
          _buildButtonsGroup([
            AppButton(
              text: '加载中',
              type: ButtonType.primary,
              loading: true,
              onPressed: () {
                print('加载状态....');
              },
            ),
            AppButton(text: '加载中', type: ButtonType.success, loading: true, onPressed: () {}),
            AppButton(loading: true, type: ButtonType.primary, circle: true, onPressed: () {}),
            AppButton(text: '加载中', type: ButtonType.warning, loading: true, onPressed: () {}),
          ]),

          _buildSectionTitle('自适应宽度 vs 占满宽度'),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text('自适应宽度（默认）：', style: TextStyle(fontWeight: FontWeight.w500)),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: EdgeInsets.all(16),
                width: double.infinity,
                child: Center(
                  child: AppButton(text: '自适应宽度按钮', type: ButtonType.primary, onPressed: () {}),
                ),
              ),

              SizedBox(height: 16),

              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text('占满宽度（使用expand=true）：', style: TextStyle(fontWeight: FontWeight.w500)),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: EdgeInsets.all(16),
                width: double.infinity,
                child: Center(
                  child: AppButton(
                    text: '占满宽度按钮',
                    type: ButtonType.primary,
                    expand: true,
                    onPressed: () {},
                  ),
                ),
              ),

              SizedBox(height: 16),

              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text('在Row中的按钮：', style: TextStyle(fontWeight: FontWeight.w500)),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: EdgeInsets.all(16),
                width: double.infinity,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppButton(text: '取消', type: ButtonType.default_, onPressed: () {}),
                    SizedBox(width: 16),
                    AppButton(text: '确认', type: ButtonType.primary, onPressed: () {}),
                  ],
                ),
              ),

              SizedBox(height: 16),

              Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Text('在Row中的Expanded按钮：', style: TextStyle(fontWeight: FontWeight.w500)),
              ),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                  borderRadius: BorderRadius.circular(4),
                ),
                padding: EdgeInsets.all(16),
                width: double.infinity,
                child: Row(
                  children: [
                    Expanded(
                      child: AppButton(
                        text: '取消',
                        type: ButtonType.default_,
                        expand: true,
                        onPressed: () {},
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: AppButton(
                        text: '确认',
                        type: ButtonType.primary,
                        expand: true,
                        onPressed: () {},
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建分组标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 16),
      child: Text(title, style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
    );
  }

  /// 构建按钮组
  Widget _buildButtonsGroup(List<Widget> buttons) {
    return Wrap(
      crossAxisAlignment: WrapCrossAlignment.center,
      spacing: 12,
      runSpacing: 12,
      children: buttons,
    );
  }
}
