import 'package:flutter/material.dart';
import 'package:octasync_client/api/job.dart';
import 'package:octasync_client/api/positions.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';
import 'package:octasync_client/views/admin/organization/position_grade/enum.dart';
import 'package:octasync_client/views/admin/organization/position_grade/model/grade_model.dart';
import 'package:octasync_client/views/admin/organization/position_grade/model/position_model.dart';

/// 弹窗类型
enum DialogTypeEmun { create, edit }

/// 职级弹窗组件
class PositionDialog extends StatefulWidget {
  final Widget? child;
  final void Function()? onSuccess; // 提交成功回调
  const PositionDialog({super.key, this.child, this.onSuccess});

  @override
  State<PositionDialog> createState() => PositionDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class PositionDialogState extends State<PositionDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;

  /// 当前弹窗类型
  DialogTypeEmun _dialogType = DialogTypeEmun.create;

  /// 表单数据
  PositionModel _positionData = PositionModel();

  /// 职级选项
  List<SelectOption<String?>> _positionidOptions = [];

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final SelectController<int> _gradeSelectController = SelectController<int>();
  final SelectController<String?> _positionIdController = SelectController<String?>();
  final TextEditingController _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  /// 重置数据
  void resetFormData([StateSetter? setDialogState]) {
    _positionData = PositionModel();
    _nameController.text = '';
    _gradeSelectController.clear();
    _positionIdController.clear(); // 同时清空职级控制器
    _positionidOptions = []; // 清空职级选项
    _descriptionController.text = '';
  }

  /// 通过详情接口获取并填充编辑数据
  Future<void> fillEditData(String id) async {
    try {
      // 调用详情接口获取完整数据
      final response = await JobApi.getDetails({'Id': id});

      if (response != null) {
        // 将响应数据转换为 PositionModel
        _positionData = PositionModel.fromJson(response);

        // 填充表单数据
        _nameController.text = _positionData.name ?? '';
        _gradeSelectController.setValue(_positionData.levelSequenceenum);
        _positionIdController.setValue(_positionData.positionId);
        _descriptionController.text = _positionData.description ?? '';
      }
    } catch (e) {
      ToastManager.error('获取详情失败');
    }
  }

  /// 提交数据 - 支持创建和编辑
  Future<void> submitRequest(BuildContext context, StateSetter setDialogState) async {
    print(_positionData.toJson());
    // 使用 Form 的校验功能
    if (!_formKey.currentState!.validate()) {
      ToastManager.error('请填写完整信息');
      return;
    }

    setDialogState(() {
      btnLoading = true;
    });

    try {
      const apiMap = {DialogTypeEmun.create: JobApi.add, DialogTypeEmun.edit: JobApi.edit};
      await apiMap[_dialogType]!(_positionData);

      ToastManager.success('操作成功');

      setDialogState(() {
        btnLoading = false;
      });

      // 只有创建模式才重置表单（编辑模式直接关闭）
      if (_dialogType == DialogTypeEmun.create) {
        resetFormData(setDialogState);
      }

      widget.onSuccess?.call();

      // 创建模式且不继续添加，或编辑模式，则关闭弹窗
      if (mounted && (_dialogType == DialogTypeEmun.edit || !isAddNext)) {
        Navigator.of(context).pop();
      }
    } catch (err) {
      setDialogState(() {
        btnLoading = true;
      });
    }
  }

  /// 获取职级
  Future<void> getPositionReq() async {
    try {
      final res = await PositionsApi.getListPage({
        'LevelSequenceenum': _positionData.levelSequenceenum,
      });
      final pages = PagesModel<GradeModel>.fromJson(
        res,
        (json) => GradeModel.fromJson(json as Map<String, dynamic>),
      );
      _positionidOptions =
          pages.items.map((e) => SelectOption(value: e.id, label: e.name ?? '')).toList();
    } catch (e) {
      _positionidOptions = [];
      ToastManager.error('获取职级失败');
    } finally {
      _positionIdController.setOptions(_positionidOptions);
    }
  }

  /// 打开职位弹窗 - 支持创建和编辑
  Future<void> showPositionDialog(
    BuildContext context, {
    DialogTypeEmun type = DialogTypeEmun.create,
    String? id,
  }) async {
    _dialogType = type;

    // 重置表单数据
    resetFormData();

    // 如果是编辑模式，先加载数据再显示弹窗
    if (type == DialogTypeEmun.edit && id != null) {
      _positionData.id = id;
      await fillEditData(id);
      await getPositionReq();
    }

    // 检查组件是否仍然挂载
    if (!mounted) return;

    double labelWidth = 80;

    /// 名称
    Widget buildNameInput() {
      return AppInput(
        label: "名称",
        required: true,
        labelWidth: labelWidth,
        labelPosition: LabelPosition.left,
        hintText: "名称",
        size: InputSize.medium,
        controller: _nameController,
        maxLength: 30,
        validator: (value) {
          // 优先使用 controller 的值进行验证，确保编辑模式下能正确获取值
          final currentValue = value ?? _nameController.text;
          if (currentValue.isEmpty) {
            return '请输入名称';
          }
          return null;
        },
        onChanged: (value) {
          _positionData.name = value;
        },
      );
    }

    /// 职级序列
    Widget buildPositionSelect() {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: labelWidth,
            child: Row(
              children: [
                Text('职级序列'),
                Text(' *', style: TextStyle(fontSize: 20, color: AppColors.error)),
              ],
            ),
          ),
          Expanded(
            child: FormField<int>(
              validator: (value) {
                if (_gradeSelectController.value == null) {
                  return '请选择职级序列';
                }
                return null;
              },
              builder: (FormFieldState<int> field) {
                return Column(
                  children: [
                    AppSelect<int>(
                      placeholder: '职级序列',
                      options: PositionGradeEnum.positionOptions,
                      controller: _gradeSelectController,
                      onChanged: (value) {
                        field.didChange(value);
                        _positionData.levelSequenceenum = value;

                        _positionIdController.clear();
                        _positionData.positionId = null;
                        getPositionReq();
                      },
                    ),

                    SizedBox(
                      height: 18,
                      child: Align(
                        alignment: Alignment.topLeft,
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 200),
                          opacity: field.hasError ? 1.0 : 0.0,
                          child: Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Text(
                              field.hasError ? (field.errorText ?? '') : '',
                              style: TextStyle(fontSize: 11, color: AppColors.error, height: 1.2),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      );
    }

    /// 职级
    Widget buildPositionIdSelect() {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: labelWidth,
            child: Row(
              children: [
                Text('职级'),
                Text(' *', style: TextStyle(fontSize: 20, color: AppColors.error)),
              ],
            ),
          ),
          Expanded(
            child: FormField<String?>(
              validator: (value) {
                if (_positionIdController.value == null) {
                  return '请选择职级';
                }
                return null;
              },
              builder: (FormFieldState<String?> field) {
                return Column(
                  children: [
                    // 使用 ListenableBuilder 监听职级序列控制器的变化，确保UI能够正确更新
                    ListenableBuilder(
                      listenable: _gradeSelectController,
                      builder: (context, _) {
                        return AppSelect<String?>(
                          placeholder: '职级',
                          options: _positionidOptions,
                          disabled: _gradeSelectController.value == null,
                          controller: _positionIdController,
                          onChanged: (value) {
                            field.didChange(value);
                            _positionData.positionId = value;
                          },
                        );
                      },
                    ),

                    SizedBox(
                      height: 18,
                      child: Align(
                        alignment: Alignment.topLeft,
                        child: AnimatedOpacity(
                          duration: const Duration(milliseconds: 200),
                          opacity: field.hasError ? 1.0 : 0.0,
                          child: Padding(
                            padding: const EdgeInsets.only(top: 4.0),
                            child: Text(
                              field.hasError ? (field.errorText ?? '') : '',
                              style: TextStyle(fontSize: 11, color: AppColors.error, height: 1.2),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      );
    }

    // 职责说明
    Widget buildDescription() {
      return AppInput(
        label: "职责说明",
        labelWidth: labelWidth,
        labelPosition: LabelPosition.left,
        hintText: "职责说明",
        size: InputSize.medium,
        controller: _descriptionController,
        maxLines: 5,
        maxLength: 3000,
        onChanged: (value) {
          _positionData.description = value;
        },
      );
    }

    AppDialog.show(
      width: 480,
      context: context,
      title: _dialogType == DialogTypeEmun.create ? '添加职位' : '编辑职位',
      isDrawer: true,
      slideDirection: SlideDirection.right,
      showFooter: false,
      barrierDismissible: true,
      padding: EdgeInsetsGeometry.zero,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          // 如果是编辑模式且有ID，异步加载数据
          if (type == DialogTypeEmun.edit && id != null && _positionData.name == null) {
            fillEditData(id);
          }

          return Column(
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(10),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      spacing: 10,
                      children: [
                        buildNameInput(),
                        buildPositionSelect(),
                        buildPositionIdSelect(),
                        buildDescription(),
                      ],
                    ),
                  ),
                ),
              ),
              Divider(),
              Padding(
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // 只有创建模式才显示"继续新建下一条"选项
                    if (_dialogType == DialogTypeEmun.create) ...[
                      Checkbox(
                        value: isAddNext,
                        onChanged: (value) {
                          setDialogState(() {
                            isAddNext = !isAddNext;
                          });
                        },
                      ),
                      Text('继续新建下一条'),
                      const SizedBox(width: 10),
                    ],
                    AppButton(
                      text: '取消',
                      type: ButtonType.default_,
                      onPressed: () => context.pop(),
                    ),
                    const SizedBox(width: 10),
                    AppButton(
                      text: '确定',
                      type: ButtonType.primary,
                      loading: btnLoading,
                      onPressed: () => submitRequest(context, setDialogState),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _gradeSelectController.dispose();
    _positionIdController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child ?? SizedBox();
  }
}
