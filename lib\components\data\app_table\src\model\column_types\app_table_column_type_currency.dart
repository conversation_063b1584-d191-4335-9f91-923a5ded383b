import 'package:octasync_client/components/data/app_table/src/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/src/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column_type_has_format.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column_type_with_number_format.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column_type_with_request_field.dart';
import 'package:intl/intl.dart' as intl;
import 'package:json_annotation/json_annotation.dart';

part 'app_table_column_type_currency.g.dart';

@JsonSerializable()
class AppTableColumnTypeCurrency
    with
        AppTableColumnTypeDefaultMixin,
        AppTableColumnTypeWithNumberFormat,
        AppTableColumnTypeWithRequestField
    implements AppTableColumnType, AppTableColumnTypeHasFormat<String> {
  dynamic _defaultValue;
  @override
  dynamic get defaultValue => _defaultValue;
  @override
  set defaultValue(dynamic value) => _defaultValue = value;

  @override
  bool isSaveRequired = false;

  @override
  bool isSubmitRequired = false;

  String _columnDesc;
  @override
  String get columnDesc => _columnDesc;
  @override
  set columnDesc(String value) => _columnDesc = value;

  @override
  ColumnTypeEnum typeCode = ColumnTypeEnum.currency;
  // @override
  // final intl.NumberFormat numberFormat;
  @override
  intl.NumberFormat get numberFormat {
    return intl.NumberFormat(
      AppTableGeneralHelper.getFormatStr(
        isShowPercentiles: isShowPercentiles,
        precision: precision,
        isRetainDecimal: isRetainDecimal,
      ),
      locale,
    );
  }

  @override
  final bool negative;

  @override
  final int decimalPoint;

  @override
  final bool allowFirstDot;

  @override
  final String? locale;

  @override
  final String format;

  @override
  final bool applyFormatOnInit;

  /// 货币类型
  int currencyType = 1;

  /// 精度（保留小数位数）
  @override
  int precision = 0;

  // /// 是否显示千分位（不能修改，默认显示）
  @override
  final bool isShowPercentiles = true;

  @override
  final bool isRetainDecimal = true;

  AppTableColumnTypeCurrency({
    dynamic defaultValue,
    String columnDesc = '',
    required this.negative,
    required this.format,
    required this.applyFormatOnInit,
    required this.allowFirstDot,
    required this.locale,
    int currencyType = 1,
    int precision = 0,
  }) : _defaultValue = defaultValue,
       //  numberFormat = intl.NumberFormat(format, locale),
       _columnDesc = columnDesc,
       decimalPoint = _getDecimalPoint(format);

  static int _getDecimalPoint(String format) {
    final int dotIndex = format.indexOf('.');

    return dotIndex < 0 ? 0 : format.substring(dotIndex).length - 1;
  }

  factory AppTableColumnTypeCurrency.fromJson(Map<String, dynamic> json) =>
      _$AppTableColumnTypeCurrencyFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AppTableColumnTypeCurrencyToJson(this);
}
