import 'package:flutter/material.dart';
import 'package:octasync_client/views/admin/project_management/project_config/wbs_config/index.dart';
import 'project_menu.dart';

class ProjectConfigPage extends StatefulWidget {
  const ProjectConfigPage({super.key});

  @override
  State<ProjectConfigPage> createState() => _ProjectConfigPageState();
}

class _ProjectConfigPageState extends State<ProjectConfigPage> {
  // 选中的菜单项
  MenuItem? selectedMenuItem;

  // 菜单数据
  final List<MenuItem> menuItems = [
    MenuItem(
      title: 'WBS视图',
      subItems: [
        MenuItem(title: 'WBS视图', contentWidget: WebConfig()),
        MenuItem(title: '任务清单', contentWidget: Center(child: Text('任务清单'))),
        MenuItem(title: '项目审批配置', contentWidget: Center(child: Text('项目审批配置'))),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
  }

  // 处理菜单项选择
  void _handleMenuSelected(MenuItem menuItem) {
    setState(() {
      selectedMenuItem = menuItem;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // 左侧菜单区域
          SizedBox(
            width: 200,
            child: ProjectMenu(
              selectedMenuItem: selectedMenuItem?.title,
              onMenuSelected: _handleMenuSelected,
              menuItems: menuItems,
              expandFirstParent: true, // 启用展开第一个父菜单到最后一级并选择第一项
              expandAll: false,
            ),
          ),

          // 右侧内容区域
          Expanded(
            child: SizedBox(
              height: double.infinity,
              child: selectedMenuItem?.contentWidget ?? const SizedBox(),
            ),
          ),
        ],
      ),
    );
  }
}
