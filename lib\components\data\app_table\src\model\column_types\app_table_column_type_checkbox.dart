// import 'package:octasync_client/components/data/app_table/app_table_column_type.dart';
// import 'package:octasync_client/components/data/app_table/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/src/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/src/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/src/model/app_table_column_type_with_request_field.dart';

import 'package:json_annotation/json_annotation.dart';

part 'app_table_column_type_checkbox.g.dart';

@JsonSerializable()
class AppTableColumnTypeCheckbox
    with AppTableColumnTypeDefaultMixin, AppTableColumnTypeWithRequestField
    implements AppTableColumnType {
  dynamic _defaultValue;
  @override
  dynamic get defaultValue => _defaultValue;
  @override
  set defaultValue(dynamic value) => _defaultValue = value;

  @override
  bool isSaveRequired = false;

  @override
  bool isSubmitRequired = false;

  String _columnDesc;
  @override
  String get columnDesc => _columnDesc;
  @override
  set columnDesc(String value) => _columnDesc = value;

  @override
  ColumnTypeEnum typeCode = ColumnTypeEnum.checkbox;

  AppTableColumnTypeCheckbox({
    dynamic defaultValue = false,
    String columnDesc = '',
    this.isSaveRequired = false,
    this.isSubmitRequired = false,
  }) : _defaultValue = defaultValue,
       _columnDesc = columnDesc;

  @override
  bool isValid(dynamic value) {
    return value is String || value is num;
  }

  @override
  int compare(dynamic a, dynamic b) {
    return AppTableGeneralHelper.compareWithNull(a, b, () => a.toString().compareTo(b.toString()));
  }

  @override
  dynamic markCompareValue(dynamic value) {
    return value.toString();
  }

  factory AppTableColumnTypeCheckbox.fromJson(Map<String, dynamic> json) =>
      _$AppTableColumnTypeCheckboxFromJson(json);
  @override
  Map<String, dynamic> toJson() => _$AppTableColumnTypeCheckboxToJson(this);
}
