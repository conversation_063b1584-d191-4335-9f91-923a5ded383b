import 'package:json_annotation/json_annotation.dart';

part 'pages_model.g.dart';

@JsonSerializable(genericArgumentFactories: true)
class PagesModel<T> {
  @Json<PERSON>ey(name: 'Items', defaultValue: [])
  List<T> items;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'PageIndex', defaultValue: 1)
  int pageIndex;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'PageSize', defaultValue: 20)
  int pageSize;
  @<PERSON>son<PERSON><PERSON>(name: 'Total', defaultValue: 0)
  int total;
  @Json<PERSON>ey(name: 'PageCount', defaultValue: 1)
  int pageCount;

  PagesModel({
    this.items = const [],
    this.pageIndex = 1,
    this.pageSize = 20,
    this.total = 0,
    this.pageCount = 1,
  });

  factory PagesModel.fromJson(Map<String, dynamic> json, T Function(Object? json) fromJsonT) {
    return _$PagesModelFromJson(json, fromJsonT);
  }

  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$PagesModelToJson(this, toJsonT);
}
