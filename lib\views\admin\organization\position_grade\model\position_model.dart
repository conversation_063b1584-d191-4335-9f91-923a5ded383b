import 'package:json_annotation/json_annotation.dart';

part 'position_model.g.dart';

@JsonSerializable()
class PositionModel {
  @Json<PERSON><PERSON>(name: 'Id')
  String? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'Name', defaultValue: '')
  String? name;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'LevelSequenceenum')
  int? levelSequenceenum;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'PositionId')
  String? positionId;
  @Json<PERSON>ey(name: 'OrderIndex')
  int? orderIndex;
  @JsonKey(name: 'Description', defaultValue: '')
  String? description;

  PositionModel({
    this.id,
    this.name = '',
    this.levelSequenceenum,
    this.positionId,
    this.orderIndex,
    this.description = '',
  });

  factory PositionModel.fromJson(Map<String, dynamic> json) {
    return _$PositionModelFromJson(json);
  }

  Map<String, dynamic> toJson() => _$PositionModelToJson(this);
}
