import 'package:flutter/material.dart';
import 'package:octasync_client/views/projects/components/assign_tab.dart';
import 'package:octasync_client/views/projects/components/config_tab.dart';
import 'package:octasync_client/views/projects/components/items_tab.dart';
import 'package:octasync_client/views/projects/components/sidebar.dart';
import 'package:octasync_client/views/projects/components/projects_tab.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/views/projects/providers/sidebar_provider.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class ProjectsIndex extends StatefulWidget {
  const ProjectsIndex({super.key});

  @override
  State<ProjectsIndex> createState() => _ProjectsIndexState();
}

class _ProjectsIndexState extends State<ProjectsIndex> {
  final double titleHeight = Variables.titleHeight;

  @override
  Widget build(BuildContext context) {
    var borderColor = Variables.commonBorderColor(context);

    return Scaffold(
      body: ChangeNotifierProvider(
        create: (context) => SideBarProvider(),
        child: Row(
          children: [
            // 左侧
            Selector<SideBarProvider, bool>(
              selector: (context, provider) => provider.showSideBar,
              builder: (context, isShowSideBar, child) {
                return isShowSideBar
                    ? Container(
                      decoration: BoxDecoration(
                        border: Border(
                          right: BorderSide(
                            color: borderColor, // 边框颜色
                            width: 1.0, // 边框宽度
                          ),
                        ),
                      ),
                      child: SizedBox(
                        width: 200,
                        child: Column(
                          children: [
                            Container(
                              height: titleHeight,
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                    color: borderColor, // 边框颜色
                                    width: 1.0, // 边框宽度
                                  ),
                                ),
                              ),
                              child: Padding(
                                padding: EdgeInsets.only(left: 5, right: 5),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Text(
                                      '项目',
                                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SideBar(),
                          ],
                        ),
                      ),
                    )
                    : SizedBox();
              },
            ),

            // 右侧 - 使用IndexedStack替代条件渲染
            Expanded(
              child: Selector<SideBarProvider, int>(
                selector: (context, provider) => provider.currentCheckedId,
                builder: (context, currentCheckedId, child) {
                  // 使用IndexedStack保持所有页面状态
                  return IndexedStack(
                    index: currentCheckedId - 1, // 索引从0开始，而ID从1开始
                    children: [ProjectsTab(), ItemsTab(), AssignTab(), ConfigTab()],
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
