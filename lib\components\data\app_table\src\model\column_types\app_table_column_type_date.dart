import 'package:octasync_client/components/data/app_table/src/app_table_column_type.dart';
import 'package:octasync_client/components/data/app_table/src/enums/column_type_enum.dart';
import 'package:octasync_client/components/data/app_table/src/enums/select_enum.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_common.dart';
import 'package:octasync_client/components/data/app_table/src/helper/app_table_general_helper.dart';
import 'package:octasync_client/components/data/app_table/src/model/general_option.dart';
import 'package:intl/intl.dart' as intl;
import 'package:json_annotation/json_annotation.dart';

part 'app_table_column_type_date.g.dart';

@JsonSerializable()
class AppTableColumnTypeDate with AppTableColumnTypeDefaultMixin implements AppTableColumnType {
  dynamic _defaultValue;
  @override
  dynamic get defaultValue => _defaultValue;
  @override
  set defaultValue(dynamic value) => _defaultValue = value;

  bool isSaveRequired = false;

  bool isSubmitRequired = false;

  String _columnDesc;
  @override
  String get columnDesc => _columnDesc;
  @override
  set columnDesc(String value) => _columnDesc = value;

  /// 日期格式
  /// 1：yyyy/MM/dd
  /// 2：yyyy-MM-dd
  /// 3：yyyy年MM月dd日
  /// 4：yyyy年MM
  /// 5：MM/dd
  int dateType = 1;

  /// 显示星期
  bool showWeek = false;

  /// 显示时间
  bool showTime = false;

  /// 默认值类型（1：无；2：指定日期；3：添加此记录的日期）
  int defaultValueType = 1;

  @override
  ColumnTypeEnum typeCode = ColumnTypeEnum.date;

  intl.DateFormat get dateFormat {
    var format =
        AppTableCommon.getDateFormatOption(dateType).text +
        (showWeek ? ' EEEE' : '') +
        (showTime ? ' HH:mm' : '');
    return intl.DateFormat(format, 'zh');
  }

  AppTableColumnTypeDate({
    dynamic defaultValue,
    String columnDesc = '',
    this.dateType = 1,
    this.showWeek = false,
    this.showTime = false,
    this.defaultValueType = 1,
  }) : _defaultValue = defaultValue,
       _columnDesc = columnDesc;

  @override
  bool isValid(dynamic value) {
    return value is DateTime;
  }

  @override
  int compare(dynamic a, dynamic b) {
    return AppTableGeneralHelper.compareWithNull(a, b, () => a.toString().compareTo(b.toString()));
  }

  @override
  dynamic markCompareValue(dynamic value) {
    return value.toString();
  }

  factory AppTableColumnTypeDate.fromJson(Map<String, dynamic> json) =>
      _$AppTableColumnTypeDateFromJson(json);
  @override
  Map<String, dynamic> toJson() {
    final json = _$AppTableColumnTypeDateToJson(this);

    // 因为 defaultValue 是dynamic 类型，将DateTime 赋值给 该类型后，无法转成 json（调用toJson报错），所以需要自行处理
    if (defaultValue is DateTime) {
      json['defaultValue'] = (defaultValue as DateTime).toIso8601String();
    }
    return json;
  }
}
