import 'package:json_annotation/json_annotation.dart';

part 'role.g.dart';

@JsonSerializable()
class Role {
  @Json<PERSON><PERSON>(name: 'Id')
  String? id;

  @Json<PERSON>ey(name: 'Name', defaultValue: '')
  String name;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'GroupId', defaultValue: '')
  String groupId;

  @<PERSON>son<PERSON>ey(name: 'ParentIdList', defaultValue: [])
  List<String> parentIdList;

  /// 系统配置 1.是 2.不是
  @<PERSON>son<PERSON><PERSON>(name: 'Defaultenum')
  final int defaultenum;

  /// 是否显示（用于UI）
  final bool isVisible;

  Role({
    this.id,
    this.name = '',
    this.groupId = '',
    this.parentIdList = const [],
    this.defaultenum = 2,
    this.isVisible = false,
  });

  factory Role.fromJson(Map<String, dynamic> json) => _$RoleFromJson(json);

  Map<String, dynamic> toJson() => _$RoleToJson(this);
}
