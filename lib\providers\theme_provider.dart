import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

enum ThemeMode { system, light, dark }

class ThemeProvider with ChangeNotifier {
  ThemeProvider(BuildContext context) {
    _loadTheme(context);
  }

  /// 主题模式
  ThemeMode _themeMode = ThemeMode.system;
  ThemeMode get themeMode => _themeMode;

  /// 加载当前主题(web端默认Light主题)
  Future<void> _loadTheme(BuildContext context) async {
    if (AppUtil.isDesktop) {
      final String savedThemeMode = await StorageUtil.getThemeMode();
      _themeMode = _stringToThemeMode(savedThemeMode);
    } else {
      _themeMode = ThemeMode.light;
    }
    notifyListeners();
  }

  /// 是否为暗色模式
  bool get isDarkMode => _getIsDarkMode();

  /// 根据当前主题模式判断是否为暗色模式
  bool _getIsDarkMode([BuildContext? context]) {
    switch (_themeMode) {
      case ThemeMode.system:
        return _getSystemBrightness(context) == Brightness.dark;
      case ThemeMode.dark:
        return true;
      case ThemeMode.light:
        return false;
    }
  }

  /// 当前主题数据
  ThemeData get themeData => isDarkMode ? AppTheme.dark() : AppTheme.light();

  /// 获取当前系统亮度
  Brightness _getSystemBrightness([BuildContext? context]) {
    return context != null
        ? MediaQuery.of(context).platformBrightness
        : WidgetsBinding.instance.platformDispatcher.platformBrightness;
  }

  /// 从字符串转换为ThemeMode
  ThemeMode _stringToThemeMode(String value) {
    return ThemeMode.values.firstWhere(
      (mode) => mode.name == value,
      orElse: () => ThemeMode.system,
    );
  }

  /// 切换亮暗主题
  void toggleTheme() {
    setThemeMode(isDarkMode ? ThemeMode.light : ThemeMode.dark);
  }

  /// 设置特定主题模式
  Future<void> setThemeMode(ThemeMode mode, [BuildContext? context]) async {
    if (_themeMode == mode) return;

    _themeMode = mode;
    await StorageUtil.setThemeMode(mode.name);
    notifyListeners();
  }
}
