import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/data/app_table/src/app_select.dart';
import 'package:octasync_client/components/data/app_table/src/enums/select_enum.dart';
import 'package:octasync_client/components/data/app_table/src/model/general_option.dart';
import 'package:uuid/uuid.dart';

enum OptionType { text, number, checkbox }

class AppSelectDemo extends StatefulWidget {
  const AppSelectDemo({super.key});

  @override
  State<AppSelectDemo> createState() => _AppSelectDemoState();
}

int selected1 = 1;
List<GeneralOption<int>> option1 = [
  GeneralOption(id: 1, text: 'Option 1'),
  GeneralOption(id: 2, text: 'Option 2'),
  GeneralOption(id: 3, text: 'Option 3'),
];

final Uuid _uuid = Uuid();
List<GeneralOption<String>> option2 = List.generate(5, (index) {
  var item = GeneralOption(id: _uuid.v4(), text: 'Option ${index + 1}');
  if (index == 0) {
    item.text = 'Option 1111111111122222222211111111111111111111111';
  }
  return item;
});
String selected2 = option2[0].id;

OptionType selected3 = OptionType.text;
List<GeneralOption<OptionType>> option3 = [
  GeneralOption(id: OptionType.text, text: '文本'),
  GeneralOption(id: OptionType.number, text: '数字'),
  GeneralOption(id: OptionType.checkbox, text: '复选框'),
];

List<int> selected4 = [1, 2, 3];
List<GeneralOptionGroup<int>> option4 = [
  GeneralOptionGroup(
    text: '分组1',
    items: [
      GeneralOption(id: 1, text: 'Option 1'),
      GeneralOption(id: 2, text: 'Option 2'),
      GeneralOption(id: 3, text: 'Option 3'),
    ],
  ),
  GeneralOptionGroup(text: '分组2', items: [GeneralOption(id: 4, text: 'Option 4')]),
];

class _AppSelectDemoState extends State<AppSelectDemo> {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        // mainAxisSize: MainAxisSize.max,
        children: [
          // _buildDemo1(),
          // _buildDemo2(),
          Row(children: [_buildDemo1(), _buildDemo2(), _buildDemo3(), _buildDemo4()]),
        ],
      ),
    );
  }

  Widget _buildDemo1() {
    return Container(
      width: 200,
      child: Column(
        children: [
          Container(child: Text('id为int类型')),
          Container(child: Text('选中：$selected1')),
          AppSelect<int>(
            value: selected1,
            options: option1,
            onChanged: (item) {
              setState(() {
                selected1 = item;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDemo2() {
    return Container(
      width: 200,
      child: Column(
        children: [
          Container(child: Text('id为String类型')),
          Container(child: Text('选中：$selected2')),
          AppSelect<String>(
            value: selected2,
            options: option2,
            onChanged: (item) {
              setState(() {
                selected2 = item;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDemo3() {
    return Container(
      width: 200,
      child: Column(
        children: [
          Container(child: Text('id为enum类型')),
          Container(child: Text('选中：$selected3')),
          AppSelect<OptionType>(
            value: selected3,
            options: option3,
            onChanged: (item) {
              setState(() {
                selected3 = item;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildDemo4() {
    return Container(
      width: 340,
      child: Column(
        children: [
          Container(child: Text('分组、多选例子')),
          Container(child: Text('选中：$selected4')),

          TextButton(
            onPressed: () {
              selected4 = [1, 2];
              print(selected4);
              setState(() {});
            },
            child: Text('选中前两项'),
          ),
          TextButton(
            onPressed: () {
              selected4 = [3, 4];
              print(selected4);
              setState(() {});
            },
            child: Text('选中后两项'),
          ),

          AppSelect<int>(
            value: selected4,
            options: option4,
            // isMultiple: true,
            subType: SelectEnum.multiple,
            onChanged: (item) {
              setState(() {
                selected4 = item;
              });
            },
          ),
        ],
      ),
    );
  }
}
