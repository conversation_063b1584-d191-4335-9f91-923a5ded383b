// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pages_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PagesModel<T> _$PagesModelFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) => PagesModel<T>(
  items: (json['Items'] as List<dynamic>?)?.map(fromJsonT).toList() ?? [],
  pageIndex: (json['PageIndex'] as num?)?.toInt() ?? 1,
  pageSize: (json['PageSize'] as num?)?.toInt() ?? 20,
  total: (json['Total'] as num?)?.toInt() ?? 0,
  pageCount: (json['PageCount'] as num?)?.toInt() ?? 1,
);

Map<String, dynamic> _$PagesModelToJson<T>(
  PagesModel<T> instance,
  Object? Function(T value) toJsonT,
) => <String, dynamic>{
  'Items': instance.items.map(toJsonT).toList(),
  'PageIndex': instance.pageIndex,
  'PageSize': instance.pageSize,
  'Total': instance.total,
  'PageCount': instance.pageCount,
};
