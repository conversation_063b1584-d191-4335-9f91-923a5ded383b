import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:octasync_client/views/login/provider/login_provider.dart';
import 'package:provider/provider.dart';
import 'package:crypto/crypto.dart';
import 'package:octasync_client/api/employee.dart';
import 'package:octasync_client/providers/user_provider.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 登录表单
class LoginForm extends StatefulWidget {
  const LoginForm({super.key});

  @override
  State<LoginForm> createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _accountController = TextEditingController();
  final TextEditingController _passwordController = TextEditingController();

  bool _rememberPassword = false;
  bool _obscureText = true;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _getSavedCredential();
  }

  /// 获取已保存的凭据
  Future<void> _getSavedCredential() async {
    final userCredential = await StorageUtil.getUserCredential();
    setState(() {
      _accountController.text = userCredential.account;
      _rememberPassword = userCredential.rememberPassword;
      if (userCredential.rememberPassword) _passwordController.text = userCredential.password;
    });
  }

  // 保存登录凭据
  Future<void> _saveCredential(UserInfo userInfo) async {
    UserCredential userCredential = UserCredential(
      account: _accountController.text,
      password: _rememberPassword ? _passwordController.text : '',
      rememberPassword: _rememberPassword,
      avatar: userInfo.avatar,
    );
    await StorageUtil.setUserCredential(userCredential);
  }

  /// 登录
  Future<void> login() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      setState(() {
        _loading = true;
      });
      final userProvider = Provider.of<UserProvider>(context, listen: false);
      final router = GoRouter.of(context);

      final passwordBytes = utf8.encode(_passwordController.text);
      final passwordMd5 = md5.convert(passwordBytes).toString();
      final params = {'Number': _accountController.text, 'PassWord': passwordMd5};

      final res = await EmployeeApi.login(params);
      final userInfo = UserInfo.fromJson(res);

      await userProvider.setUserInfo(userInfo);
      _saveCredential(userInfo);
      ToastManager.success('登录成功');
      router.go('/');
    } finally {
      setState(() {
        _loading = false;
      });
    }
  }

  @override
  void dispose() {
    _accountController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          AppInput(
            label: "用户名",
            hintText: "用户名",
            size: InputSize.large,
            controller: _accountController,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入用户名';
              }
              return null;
            },
            onChanged: (value) {
              context.read<LoginProvider>().setInputAccount(value);
            },
          ),
          const SizedBox(height: 20),
          AppInput(
            label: "密码",
            hintText: "密码",
            controller: _passwordController,
            obscureText: _obscureText,
            size: InputSize.large,
            suffixIcon: AppButton(
              type: ButtonType.transparent,
              size: ButtonSize.small,
              iconData: _obscureText ? Icons.visibility_off : Icons.visibility,
              color: context.icon200,
              onPressed: () {
                setState(() {
                  _obscureText = !_obscureText;
                });
              },
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入密码';
              }
              if (value.length < 6) {
                return '密码长度不能少于6位';
              }
              return null;
            },
          ),
          const SizedBox(height: 20),
          AppButton(
            text: '登录',
            type: ButtonType.primary,
            size: ButtonSize.large,
            expand: true,
            loading: _loading,
            onPressed: login,
          ),
          const SizedBox(height: 10),
          // 记住密码
          Row(
            children: [
              Checkbox(
                value: _rememberPassword,
                onChanged: (value) {
                  setState(() {
                    _rememberPassword = value ?? false;
                  });
                },
              ),
              const Text('记住密码'),
            ],
          ),
        ],
      ),
    );
  }
}
