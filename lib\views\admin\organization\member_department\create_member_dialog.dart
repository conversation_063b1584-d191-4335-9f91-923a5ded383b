import 'package:flutter/material.dart';
import 'package:octasync_client/components/form/phone_input/phone_input.dart';
import 'package:octasync_client/components/selector/department_selector/index.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/role_mgmt/employee.dart';
import 'package:octasync_client/views/admin/organization/member_department/member_department_enum.dart';

/// 弹窗类型
enum DialogTypeEmun { create, edit }

// 人员创建
class CreateMemberDialog extends StatefulWidget {
  final void Function()? onSuccess; // 提交成功回调
  final Widget? child;

  const CreateMemberDialog({super.key, this.onSuccess, this.child});

  @override
  State<CreateMemberDialog> createState() => MemberDialogState();
}

// 将 State 类改为公开，以便外部可以访问
class MemberDialogState extends State<CreateMemberDialog> {
  /// 是否继续添加下一条
  bool isAddNext = false;
  bool btnLoading = false;
  Employee _employeeData = Employee();

  /// 当前弹窗类型
  DialogTypeEmun _dialogType = DialogTypeEmun.create;

  /// 选中的部门列表
  List<DepartmentModel> checkedDepartments = [];

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _employeeNameController = TextEditingController();
  final SelectController<int> _sexSelectController = SelectController<int>();

  /// 手机号控制器
  final SelectController<String> _phoneAreaCodeController = SelectController<String>();
  final TextEditingController _phoneNumberController = TextEditingController();

  /// 重置数据
  void resetFormData() {
    _employeeData = Employee();
    _employeeNameController.text = '';
    _sexSelectController.clear();
    _phoneAreaCodeController.clear();
    _phoneNumberController.text = '';
    checkedDepartments = [];
  }

  @override
  void dispose() {
    _employeeNameController.dispose();
    _sexSelectController.dispose();
    _phoneAreaCodeController.dispose();
    _phoneNumberController.dispose();
    super.dispose();
  }

  /// 添加
  Future<void> createRequest(BuildContext context, StateSetter setDialogState) async {
    if (!_formKey.currentState!.validate()) return;

    // try {
    //   departmentModel.parentIdList = checkedDepartments.map((t) => t.id!).toList();
    //   final params = departmentModel.toJson();
    //   params.remove('Id');
    //   await DepartmentApi.add(params);
    //   ToastManager.success('提交成功');
    //   widget.onSuccess?.call();
    //   resetFormData();
    //   if (!isAddNext) context.pop();
    // } finally {
    //   setState(() {
    //     btnLoading = false;
    //   });
    // }
  }

  /// 打开添加部门弹窗
  void showDepartmentDialog(
    BuildContext context, {
    DialogTypeEmun type = DialogTypeEmun.create,
    String? id,
  }) {
    // 重置表单数据
    resetFormData();

    // 设置默认值
    _phoneAreaCodeController.setValue('+86');
    _employeeData.phoneAreaCode = '+86';

    _dialogType = type;

    double labelWidth = 80;

    /// 间距
    double spacing = 20;

    /// 名称
    Widget buildNameInput() {
      return AppInput(
        label: "姓名",
        labelWidth: labelWidth,
        required: true,
        labelPosition: LabelPosition.left,
        hintText: "姓名",
        size: InputSize.medium,
        controller: _employeeNameController,
        maxLength: 30,
        validator: (value) {
          if (value == null || value.isEmpty) {
            return '请输入姓名';
          }
          return null;
        },
        onChanged: (value) {
          _employeeData.name = value;
        },
      );
    }

    /// 性别
    Widget buildSexSelect() {
      return AppFormField(
        label: '性别',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          print('>>>222----$value----${_sexSelectController.value}');
          if (_sexSelectController.value == null) {
            return '请选择性别';
          }
          return null;
        },
        builder:
            (field) => AppSelect<int>(
              placeholder: '性别',
              options: MemberDepartmentEnum.sexOptions,
              controller: _sexSelectController,
              onChanged: (value) {
                print('>>>111----$value');
                field.didChange(value); // 触发验证
                _employeeData.sexEnum = value;
              },
            ),
      );
    }

    /// 手机号
    Widget buildPhoneInput() {
      return AppFormField(
        label: '手机号码',
        required: true,
        labelWidth: labelWidth,
        labelPosition: FormFieldLabelPosition.left,
        validator: (value) {
          if (_employeeData.phoneAreaCode.isEmpty && _employeeData.phoneNumber.isEmpty) {
            return '区号和手机号不能为空';
          } else if (_employeeData.phoneAreaCode.isEmpty) {
            return '区号不能为空';
          } else if (_employeeData.phoneNumber.isEmpty) {
            return '手机号不能为空';
          }
          return null;
        },
        builder:
            (field) => PhoneInput(
              areaCodeController: _phoneAreaCodeController,
              phoneController: _phoneNumberController,
              onChange: (areaCode, phoneNumber) {
                field.didChange(phoneNumber);
                _employeeData.phoneAreaCode = areaCode ?? '+86';
                _employeeData.phoneNumber = phoneNumber ?? '';
              },
            ),
      );
    }

    AppDialog.show(
      width: 480,
      context: context,
      title: _dialogType == DialogTypeEmun.create ? '添加成员' : '编辑成员',
      isDrawer: true,
      barrierDismissible: true,
      slideDirection: SlideDirection.right,
      showFooter: false,
      child: StatefulBuilder(
        builder: (context, setDialogState) {
          return Column(
            children: [
              Expanded(
                child: Form(
                  key: _formKey,
                  autovalidateMode: AutovalidateMode.disabled,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      buildNameInput(),
                      buildSexSelect(),
                      buildPhoneInput(),
                      Row(
                        children: [
                          SizedBox(width: labelWidth, child: Text('所属部门')),
                          Expanded(
                            child: DepartmentSelector(
                              checkStrictly: true,
                              defaultCheckedDepartmentIds:
                                  checkedDepartments.map((t) => t.id!).toList(),
                              onChange: (selectedDepartments) {
                                setDialogState(() {
                                  checkedDepartments = selectedDepartments;
                                  print('Selected Departments: ${jsonEncode(checkedDepartments)}');
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              Divider(),
              Padding(
                padding: const EdgeInsets.all(10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // 只有创建模式才显示"继续新建下一条"选项
                    if (_dialogType == DialogTypeEmun.create) ...[
                      Checkbox(
                        value: isAddNext,
                        onChanged: (value) {
                          setDialogState(() {
                            isAddNext = !isAddNext;
                          });
                        },
                      ),
                      Text('继续新建下一条'),
                      const SizedBox(width: 10),
                    ],
                    AppButton(
                      text: '取消',
                      type: ButtonType.default_,
                      onPressed: () => context.pop(),
                    ),
                    const SizedBox(width: 10),
                    AppButton(
                      text: '确定',
                      type: ButtonType.primary,
                      loading: btnLoading,
                      onPressed: () => createRequest(context, setDialogState),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return widget.child ?? SizedBox();
  }
}
