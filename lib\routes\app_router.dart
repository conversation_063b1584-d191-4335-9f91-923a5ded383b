import 'package:flutter/material.dart';
import 'package:octasync_client/layout/window_bar_wrapper.dart';
import 'package:octasync_client/providers/router/base_router_provider.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/views/not_found_page.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class AppRouter {
  GoRouter createRouter(BuildContext context, {GlobalKey<NavigatorState>? navigatorKey}) {
    final routerProvider = context.read<BaseRouterProvider>();

    return GoRouter(
      initialLocation: '/',
      errorBuilder: (context, state) => WindowBarWrapper(child: NotFoundPage()),
      redirect: _handleRedirect,
      routes: routerProvider.getRoutes,
      navigatorKey: navigatorKey,
    );
  }

  // 路由守卫
  Future<String?> _handleRedirect(BuildContext context, GoRouterState state) async {
    final isLogin = await StorageUtil.getLoginStatus();
    if (isLogin == false) return '/login';
    if (state.uri.path == '/login') return '/';

    return null;
  }
}
