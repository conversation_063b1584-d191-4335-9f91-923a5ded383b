import 'package:flutter/material.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/components/selector/department_selector/department_tree.dart';

/// 部门选择器状态管理 Provider
class DepartmentSelectorProvider extends ChangeNotifier {
  /// 当前选中的部门列表
  List<DepartmentModel> _checkedDepartments = [];

  /// 搜索关键词
  String _searchQuery = '';

  /// 默认选中的部门ID列表
  List<String> _defaultCheckedDepartmentIds = [];

  /// 获取当前选中的部门列表
  List<DepartmentModel> get checkedDepartments => List.unmodifiable(_checkedDepartments);

  /// 获取当前搜索关键词
  String get searchQuery => _searchQuery;

  /// 获取选中部门数量
  int get checkedCount => _checkedDepartments.length;

  /// 获取默认选中的部门ID列表
  List<String> get defaultCheckedDepartmentIds => List.unmodifiable(_defaultCheckedDepartmentIds);

  /// 设置默认选中的部门ID列表
  void setDefaultCheckedDepartmentIds(List<String> departmentIds) {
    _defaultCheckedDepartmentIds = List.from(departmentIds);
    notifyListeners();
  }

  /// 检查部门ID是否在默认选中列表中
  bool isDefaultChecked(String departmentId) {
    return _defaultCheckedDepartmentIds.contains(departmentId);
  }

  /// 更新选中的部门列表
  void updateCheckedDepartments(List<DepartmentModel> departments) {
    _checkedDepartments = List.from(departments);
    notifyListeners();
  }

  /// 添加选中的部门
  void addCheckedDepartment(DepartmentModel department) {
    if (!_checkedDepartments.any((d) => d.id == department.id)) {
      _checkedDepartments.add(department);
      notifyListeners();
    }
  }

  /// 移除选中的部门
  void removeCheckedDepartment(String departmentId) {
    _checkedDepartments.removeWhere((d) => d.id == departmentId);
    notifyListeners();
  }

  /// 清空所有选中的部门
  void clearAllCheckedDepartments() {
    _checkedDepartments.clear();
    notifyListeners();
  }

  /// 设置搜索关键词
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  /// 重置状态
  void reset() {
    _checkedDepartments.clear();
    _searchQuery = '';
    notifyListeners();
  }

  /// 重置到默认选中状态（保留默认选中的部门）
  void resetToDefault() {
    _checkedDepartments.clear();
    _searchQuery = '';
    // 注意：这里不清空 _defaultCheckedDepartmentIds，因为它们应该保持
    notifyListeners();
  }

  /// 应用默认选中状态到部门树（数据加载完成后调用）
  void applyDefaultCheckedState(GlobalKey<DepartmentTreeState> treeKey) {
    if (_defaultCheckedDepartmentIds.isNotEmpty) {
      final treeState = treeKey.currentState;
      if (treeState != null) {
        // 先重置所有选中状态
        treeState.resetAllNodesCheck();
        // 然后设置默认选中的部门
        for (final departmentId in _defaultCheckedDepartmentIds) {
          treeState.checkNode(departmentId);
        }
        // 更新Provider中的选中列表
        final checkedDepartments = treeState.getAllCheckedDepartments();
        updateCheckedDepartments(checkedDepartments);
      }
    }
  }
}
