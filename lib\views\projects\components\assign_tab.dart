import 'package:flutter/material.dart';
import 'package:octasync_client/views/projects/components/projects_tab/tab_title.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class AssignTab extends StatefulWidget {
  const AssignTab({super.key});

  @override
  State<AssignTab> createState() => _AssignTabState();
}

class _AssignTabState extends State<AssignTab> {
  final double titleHeight = Variables.titleHeight;

  @override
  Widget build(BuildContext context) {
    var borderColor = Variables.commonBorderColor(context);

    return Scaffold(
      body: Column(
        children: [
          Container(
            height: titleHeight,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: borderColor, // 边框颜色
                  width: 1.0, // 边框宽度
                ),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.only(left: 5, right: 5),
              child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [TabTitle()]),
            ),
          ),
        ],
      ),
    );
  }
}
