import 'package:flutter/material.dart';
import 'package:octasync_client/imports.dart';
import 'package:octasync_client/components/selector/department_selector/department_tree.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/views/admin/organization/member_department/create_member_dialog.dart';

/// 成员页
class MemberPage extends StatefulWidget {
  const MemberPage({super.key});

  @override
  State<MemberPage> createState() => _MemberPageState();
}

class _MemberPageState extends State<MemberPage> {
  final GlobalKey<DepartmentTreeState> _departmentSelectorKey = GlobalKey<DepartmentTreeState>();

  final GlobalKey<MemberDialogState> _memberDialogStateKey = GlobalKey<MemberDialogState>();

  // 当前选中的部门（高亮显示）
  DepartmentModel? _selectedDepartment;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 左侧部门选择器
        Container(
          width: 250,
          decoration: BoxDecoration(border: Border(right: BorderSide(color: context.border300))),
          child: DepartmentTree(
            key: _departmentSelectorKey,
            onNodeTap: (department) {
              // 处理部门节点点击事件（高亮显示）
              setState(() {
                _selectedDepartment = department;
              });
              debugPrint('高亮选择了部门: ${department.departmentName}');
            },
          ),
        ),

        // 右侧操作区域
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Column(
              children: [
                // 顶部区域
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      _selectedDepartment?.departmentName ?? '',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    Row(
                      spacing: 10,
                      children: [
                        AppButton(
                          iconData: IconFont.xianxing_bianji,
                          type: ButtonType.primary,
                          text: '调整部门',
                          onPressed: () {},
                        ),
                        CreateMemberDialog(
                          key: _memberDialogStateKey,
                          // onSuccess: () => getList(),
                          child: AppButton(
                            iconData: IconFont.xianxing_tianjia,
                            text: '添加成员',
                            type: ButtonType.primary,
                            onPressed: () {
                              _memberDialogStateKey.currentState?.showDepartmentDialog(
                                context,
                                type: DialogTypeEmun.create,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
