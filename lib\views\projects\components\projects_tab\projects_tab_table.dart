import 'package:flutter/material.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

class ProjectsTabTable extends StatefulWidget {
  final GlobalKey<AppTabsState> tabsKey;

  const ProjectsTabTable({super.key, required this.tabsKey});

  @override
  State<ProjectsTabTable> createState() => _ProjectsTabTableState();
}

class _ProjectsTabTableState extends State<ProjectsTabTable> {
  @override
  Widget build(BuildContext context) {
    return Center(child: Text('列表视图模式 - 开发中'));
  }
}
