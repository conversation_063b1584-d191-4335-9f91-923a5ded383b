// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'app_table_column_type_single_select.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AppTableColumnTypeSingleSelect _$AppTableColumnTypeSingleSelectFromJson(
  Map<String, dynamic> json,
) =>
    AppTableColumnTypeSingleSelect(
        defaultValue: json['defaultValue'] ?? '',
        columnDesc: json['columnDesc'] as String? ?? '',
        subType:
            $enumDecodeNullable(_$SelectEnumEnumMap, json['subType']) ??
            SelectEnum.single,
      )
      ..isSaveRequired = json['isSaveRequired'] as bool
      ..isSubmitRequired = json['isSubmitRequired'] as bool
      ..options = AppTableColumnTypeSingleSelect._optionsFromJson(
        json['options'] as List?,
      )
      ..defaultValueType = (json['defaultValueType'] as num).toInt()
      ..typeCode = $enumDecode(_$ColumnTypeEnumEnumMap, json['typeCode']);

Map<String, dynamic> _$AppTableColumnTypeSingleSelectToJson(
  AppTableColumnTypeSingleSelect instance,
) => <String, dynamic>{
  'defaultValue': instance.defaultValue,
  'isSaveRequired': instance.isSaveRequired,
  'isSubmitRequired': instance.isSubmitRequired,
  'columnDesc': instance.columnDesc,
  'options': AppTableColumnTypeSingleSelect._optionsToJson(instance.options),
  'defaultValueType': instance.defaultValueType,
  'subType': _$SelectEnumEnumMap[instance.subType]!,
  'typeCode': _$ColumnTypeEnumEnumMap[instance.typeCode]!,
};

const _$SelectEnumEnumMap = {
  SelectEnum.single: 'single',
  SelectEnum.multiple: 'multiple',
};

const _$ColumnTypeEnumEnumMap = {
  ColumnTypeEnum.text: 'text',
  ColumnTypeEnum.singleSelect: 'singleSelect',
  ColumnTypeEnum.multipleSelect: 'multipleSelect',
  ColumnTypeEnum.number: 'number',
  ColumnTypeEnum.date: 'date',
  ColumnTypeEnum.percentage: 'percentage',
  ColumnTypeEnum.checkbox: 'checkbox',
  ColumnTypeEnum.hyperlink: 'hyperlink',
  ColumnTypeEnum.parentRecord: 'parentRecord',
  ColumnTypeEnum.telephone: 'telephone',
  ColumnTypeEnum.currency: 'currency',
};
