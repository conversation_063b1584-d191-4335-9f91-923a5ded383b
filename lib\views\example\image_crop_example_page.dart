import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:octasync_client/components/image_crop_dialog.dart';
import 'package:file_picker/file_picker.dart';
import 'package:octasync_client/models/upload/upload_response.dart';
import 'package:jyt_components_package/jyt_components_package.dart';

/// 图片裁剪示例页面
class ImageCropExamplePage extends StatefulWidget {
  const ImageCropExamplePage({super.key});

  @override
  State<ImageCropExamplePage> createState() => _ImageCropExamplePageState();
}

class _ImageCropExamplePageState extends State<ImageCropExamplePage> {
  /// 原始图片数据
  PlatformFile? _originalImage;

  /// 裁剪后的图片数据
  UploadResponse? _croppedImage;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('正方形图片裁剪'),
          _buildDescription('支持选择图片并进行正方形裁剪，裁剪过程中提供实时预览。'),
          _buildExampleCard(
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AppButton(text: '选择图片', type: ButtonType.default_, onPressed: _pickImage),
                    const SizedBox(width: 16),
                    AppButton(
                      text: '打开裁剪对话框',
                      type: ButtonType.primary,
                      disabled: _originalImage == null,
                      onPressed: _originalImage != null ? _openCropDialog : null,
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                _buildImageComparison(),
              ],
            ),
          ),

          _buildSectionTitle('直接选择并裁剪图片'),
          _buildDescription('直接选择图片并打开裁剪对话框。'),
          _buildExampleCard(
            child: AppButton(
              text: '选择并裁剪图片',
              type: ButtonType.primary,
              onPressed: _pickAndCropImage,
            ),
          ),
        ],
      ),
    );
  }

  /// 选择图片
  Future<void> _pickImage() async {
    PlatformFile? file = await FileUtil.pickSingleFile(type: FileType.image);
    if (file != null && file.bytes != null) {
      setState(() {
        _originalImage = file;
        _croppedImage = null; // 清除之前的裁剪结果
      });
    }
  }

  /// 打开裁剪对话框
  Future<void> _openCropDialog() async {
    if (_originalImage == null) return;

    if (!mounted) return;
    final result = await ImageCropDialog.show(context: context, imageFile: _originalImage);

    if (result != null) {
      setState(() {
        _croppedImage = result;
      });
    }
  }

  /// 直接选择并裁剪图片
  Future<void> _pickAndCropImage() async {
    PlatformFile? file = await FileUtil.pickSingleFile(type: FileType.image);
    if (file != null && file.bytes != null) {
      setState(() {
        _originalImage = file;
      });

      if (!mounted) return;
      final result = await ImageCropDialog.show(context: context, imageFile: file);

      if (result != null) {
        setState(() {
          _croppedImage = result;
        });
      }
    }
  }

  /// 构建图片比较区域
  Widget _buildImageComparison() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildImageBox('原始图片', _originalImage?.bytes, width: 200, height: 200),
        const SizedBox(width: 32),
        _buildImageBox('裁剪后图片', _croppedImage, width: 200, height: 200),
      ],
    );
  }

  /// 构建图片展示框
  Widget _buildImageBox(String title, Object? imageData, {double? width, double? height}) {
    return Column(
      children: [
        Text(title, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold)),
        const SizedBox(height: 8),
        Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child:
                imageData != null
                    ? ClipRRect(
                      borderRadius: BorderRadius.circular(6),
                      child:
                          imageData is Uint8List
                              ? Image.memory(
                                imageData,
                                width: width,
                                height: height,
                                fit: BoxFit.cover,
                              )
                              : Image.network(
                                (imageData as UploadResponse).path ?? '',
                                width: width,
                                height: height,
                                fit: BoxFit.cover,
                              ),
                    )
                    : Icon(Icons.image, size: 64, color: Colors.grey.shade400),
          ),
        ),
      ],
    );
  }

  /// 创建标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(top: 24, bottom: 8),
      child: Text(title, style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
    );
  }

  /// 创建描述文本
  Widget _buildDescription(String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(description, style: TextStyle(fontSize: 14, color: Colors.grey.shade700)),
    );
  }

  /// 创建示例卡片
  Widget _buildExampleCard({required Widget child}) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 24),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: context.backgroundWhite,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: child,
    );
  }
}
